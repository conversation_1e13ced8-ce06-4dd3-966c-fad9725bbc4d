const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
let authToken = '';
let testUserId = '';
let testPlaylistId = '';

// 辅助函数：发送请求
async function makeRequest(method, url, data = null, token = null, isFormData = false) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {}
    };
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    if (data) {
      if (isFormData) {
        config.data = data;
        // Let axios set the content-type for FormData
      } else {
        config.data = data;
        config.headers['Content-Type'] = 'application/json';
      }
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status
    };
  }
}

// 创建测试图片文件
function createTestImage() {
  const testImagePath = path.join(__dirname, 'test-cover.png');
  
  // 创建一个简单的PNG图片数据（1x1像素的透明PNG）
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // width: 1
    0x00, 0x00, 0x00, 0x01, // height: 1
    0x08, 0x06, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
    0x1F, 0x15, 0xC4, 0x89, // CRC
    0x00, 0x00, 0x00, 0x0A, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x78, 0x9C, 0x62, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // compressed data
    0xE2, 0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  fs.writeFileSync(testImagePath, pngData);
  return testImagePath;
}

// 测试用户登录
async function testLogin() {
  console.log('\n🔐 Testing user login...');
  
  const result = await makeRequest('POST', '/auth/login', {
    identifier: '<EMAIL>',
    password: 'password123'
  });
  
  if (result.success) {
    authToken = result.data.data.token;
    testUserId = result.data.data.user._id || result.data.data.user.id;
    console.log('✅ Login successful');
    console.log(`   User ID: ${testUserId}`);
    return true;
  } else {
    console.log('❌ Login failed:', result.error);
    return false;
  }
}

// 创建测试歌单
async function createTestPlaylist() {
  console.log('\n📝 Creating test playlist...');
  
  const playlistData = {
    name: '封面测试歌单',
    description: '用于测试封面上传功能的歌单',
    isPublic: true,
    tags: ['测试', '封面'],
    category: 'pop'
  };
  
  const result = await makeRequest('POST', '/playlists', playlistData, authToken);
  
  if (result.success) {
    testPlaylistId = result.data.data.playlist._id;
    console.log('✅ Test playlist created');
    console.log(`   Playlist ID: ${testPlaylistId}`);
    console.log(`   Name: ${result.data.data.playlist.name}`);
    console.log(`   Current cover: ${result.data.data.playlist.coverImage || 'None'}`);
    return true;
  } else {
    console.log('❌ Failed to create test playlist:', result.error);
    return false;
  }
}

// 测试上传歌单封面
async function testUploadPlaylistCover() {
  console.log('\n📸 Testing upload playlist cover...');
  
  // 创建测试图片
  const testImagePath = createTestImage();
  
  try {
    // 创建FormData
    const formData = new FormData();
    formData.append('cover', fs.createReadStream(testImagePath), {
      filename: 'test-cover.png',
      contentType: 'image/png'
    });
    
    const result = await makeRequest('POST', `/playlists/${testPlaylistId}/cover`, formData, authToken, true);
    
    if (result.success) {
      console.log('✅ Playlist cover uploaded successfully');
      console.log(`   Cover image: ${result.data.data.playlist.coverImage}`);
      console.log(`   Cover URL: ${result.data.data.playlist.coverUrl}`);
      console.log(`   Upload details: ${result.data.data.uploadResult.objectName}`);
      return true;
    } else {
      console.log('❌ Failed to upload playlist cover:', result.error);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Error uploading playlist cover:', error.message);
    return false;
  } finally {
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }
  }
}

// 测试获取带封面的歌单详情
async function testGetPlaylistWithCover() {
  console.log('\n🔍 Testing get playlist with cover...');
  
  const result = await makeRequest('GET', `/playlists/${testPlaylistId}`, null, authToken);
  
  if (result.success) {
    const playlist = result.data.data.playlist;
    console.log('✅ Got playlist with cover');
    console.log(`   Playlist: ${playlist.name}`);
    console.log(`   Cover image: ${playlist.coverImage || 'None'}`);
    
    if (playlist.coverImage) {
      const coverUrl = `http://localhost:9000/images/${playlist.coverImage}`;
      console.log(`   Cover URL: ${coverUrl}`);
    }
    
    return true;
  } else {
    console.log('❌ Failed to get playlist with cover:', result.error);
    return false;
  }
}

// 测试删除歌单封面
async function testRemovePlaylistCover() {
  console.log('\n🗑️ Testing remove playlist cover...');
  
  const result = await makeRequest('DELETE', `/playlists/${testPlaylistId}/cover`, null, authToken);
  
  if (result.success) {
    console.log('✅ Playlist cover removed successfully');
    console.log(`   Playlist: ${result.data.data.playlist.name}`);
    console.log(`   Current cover: ${result.data.data.playlist.coverImage || 'None'}`);
    console.log(`   Removed cover: ${result.data.data.removedCover || 'None'}`);
    return true;
  } else {
    console.log('❌ Failed to remove playlist cover:', result.error);
    return false;
  }
}

// 测试上传信息接口
async function testUploadInfo() {
  console.log('\n📋 Testing upload info...');
  
  const result = await makeRequest('GET', '/upload/info');
  
  if (result.success) {
    console.log('✅ Got upload info successfully');
    console.log(`   Max file size: ${result.data.maxFileSize}`);
    console.log(`   Allowed image formats: ${result.data.allowedImageFormats.join(', ')}`);
    console.log(`   Cover upload endpoint: ${result.data.endpoints.cover}`);
    return true;
  } else {
    console.log('❌ Failed to get upload info:', result.error);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('📸 Starting Playlist Cover Upload Tests...');
  console.log('==========================================');
  
  // 测试登录
  const loginSuccess = await testLogin();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }
  
  // 测试上传信息
  await testUploadInfo();
  
  // 创建测试歌单
  const playlistSuccess = await createTestPlaylist();
  if (!playlistSuccess) {
    console.log('\n❌ Cannot proceed without test playlist');
    return;
  }
  
  // 测试上传封面
  await testUploadPlaylistCover();
  
  // 测试获取带封面的歌单
  await testGetPlaylistWithCover();
  
  // 测试删除封面
  await testRemovePlaylistCover();
  
  // 最终检查歌单状态
  await testGetPlaylistWithCover();
  
  console.log('\n==========================================');
  console.log('🎉 Playlist Cover Upload Tests Completed!');
}

// 运行测试
runTests().catch(console.error);
