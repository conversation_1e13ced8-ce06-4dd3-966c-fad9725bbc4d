// MongoDB 初始化脚本
// 创建 musicdou 数据库和用户

// 切换到 musicdou 数据库
db = db.getSiblingDB('musicdou');

// 创建应用用户
db.createUser({
  user: 'musicdou_user',
  pwd: 'musicdou123',
  roles: [
    {
      role: 'readWrite',
      db: 'musicdou'
    }
  ]
});

// 创建基础集合
db.createCollection('users');
db.createCollection('music');
db.createCollection('playlists');
db.createCollection('uploads');

// 创建索引
// 用户索引
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "username": 1 }, { unique: true });

// 音乐索引
db.music.createIndex({ "title": "text", "artist": "text", "album": "text" });
db.music.createIndex({ "uploadedBy": 1 });
db.music.createIndex({ "createdAt": -1 });

// 播放列表索引
db.playlists.createIndex({ "userId": 1 });
db.playlists.createIndex({ "name": "text" });

// 上传记录索引
db.uploads.createIndex({ "userId": 1 });
db.uploads.createIndex({ "status": 1 });
db.uploads.createIndex({ "createdAt": -1 });

print('MongoDB 初始化完成');
