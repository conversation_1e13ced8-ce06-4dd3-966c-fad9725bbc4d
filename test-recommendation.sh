#!/bin/bash

# 音乐推荐系统测试脚本
# 测试新增的推荐系统功能

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 MusicDou 推荐系统测试${NC}"
echo -e "${BLUE}======================================${NC}"

# 检查服务器是否运行
echo -e "${YELLOW}📡 检查服务器状态...${NC}"
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo -e "${RED}❌ 服务器未运行，请先启动服务器:${NC}"
    echo -e "${YELLOW}   npm run dev${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 服务器正在运行${NC}"

echo ""
echo -e "${YELLOW}🧪 运行推荐系统测试...${NC}"
echo -e "${BLUE}=================================${NC}"

# 运行测试脚本
node test-recommendation-system.js

echo ""
echo -e "${GREEN}📋 推荐系统功能测试总结:${NC}"
echo -e "${BLUE}========================${NC}"
echo -e "${GREEN}✅ 个性化推荐 (混合算法)${NC}"
echo -e "${GREEN}✅ 相似音乐推荐${NC}"
echo -e "${GREEN}✅ 热门音乐推荐${NC}"
echo -e "${GREEN}✅ 新音乐发现${NC}"
echo -e "${GREEN}✅ 用户偏好分析${NC}"
echo -e "${GREEN}✅ 推荐反馈记录${NC}"
echo -e "${GREEN}✅ 行为分析刷新${NC}"
echo -e "${GREEN}✅ 推荐统计查询${NC}"

echo ""
echo -e "${YELLOW}🎯 测试的API端点:${NC}"
echo -e "${BLUE}==================${NC}"
echo -e "GET    /api/v1/recommendations/personalized"
echo -e "GET    /api/v1/recommendations/similar/:musicId"
echo -e "GET    /api/v1/recommendations/popular"
echo -e "GET    /api/v1/recommendations/discover"
echo -e "GET    /api/v1/recommendations/preferences"
echo -e "GET    /api/v1/recommendations/stats"
echo -e "POST   /api/v1/recommendations/feedback"
echo -e "POST   /api/v1/recommendations/analyze-behavior"

echo ""
echo -e "${YELLOW}🔧 手动测试命令:${NC}"
echo -e "${BLUE}=================${NC}"
echo ""
echo -e "${YELLOW}# 1. 获取个性化推荐 (需要认证)${NC}"
echo "curl -X GET 'http://localhost:3000/api/v1/recommendations/personalized?limit=10&algorithm=hybrid' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo -e "${YELLOW}# 2. 获取相似音乐推荐${NC}"
echo "curl -X GET 'http://localhost:3000/api/v1/recommendations/similar/MUSIC_ID?limit=5' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo -e "${YELLOW}# 3. 获取热门推荐${NC}"
echo "curl -X GET 'http://localhost:3000/api/v1/recommendations/popular?limit=10' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo -e "${YELLOW}# 4. 获取发现推荐${NC}"
echo "curl -X GET 'http://localhost:3000/api/v1/recommendations/discover?limit=10' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo -e "${YELLOW}# 5. 获取用户偏好${NC}"
echo "curl -X GET 'http://localhost:3000/api/v1/recommendations/preferences' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN'"
echo ""
echo -e "${YELLOW}# 6. 记录推荐反馈${NC}"
echo "curl -X POST 'http://localhost:3000/api/v1/recommendations/feedback' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_TOKEN' \\"
echo "  -d '{\"musicId\": \"MUSIC_ID\", \"feedbackType\": \"play\", \"playDuration\": 120}'"

echo ""
echo -e "${YELLOW}🧠 推荐算法类型:${NC}"
echo -e "${BLUE}=================${NC}"
echo -e "• ${GREEN}collaborative_filtering${NC}: 基于相似用户的协同过滤"
echo -e "• ${GREEN}content_based${NC}: 基于音乐内容特征的推荐"
echo -e "• ${GREEN}hybrid${NC}: 混合推荐算法 (协同过滤 + 内容推荐)"
echo -e "• ${GREEN}popularity${NC}: 基于流行度的推荐"
echo -e "• ${GREEN}random${NC}: 随机发现推荐"

echo ""
echo -e "${YELLOW}📊 推荐参数说明:${NC}"
echo -e "${BLUE}=================${NC}"
echo -e "• ${GREEN}limit${NC}: 推荐数量限制 (默认: 20)"
echo -e "• ${GREEN}algorithm${NC}: 指定推荐算法 (默认: 自动选择)"
echo -e "• ${GREEN}diversityWeight${NC}: 多样性权重 (0-1, 默认: 0.3)"
echo -e "• ${GREEN}noveltyWeight${NC}: 新颖性权重 (0-1, 默认: 0.2)"
echo -e "• ${GREEN}popularityWeight${NC}: 流行度权重 (0-1, 默认: 0.1)"

echo ""
echo -e "${YELLOW}📈 反馈类型:${NC}"
echo -e "${BLUE}============${NC}"
echo -e "• ${GREEN}click${NC}: 点击推荐"
echo -e "• ${GREEN}play${NC}: 播放音乐"
echo -e "• ${GREEN}complete${NC}: 完整播放"
echo -e "• ${GREEN}skip${NC}: 跳过音乐"
echo -e "• ${GREEN}favorite${NC}: 收藏音乐"
echo -e "• ${GREEN}share${NC}: 分享音乐"

echo ""
echo -e "${YELLOW}🔍 用户偏好分析:${NC}"
echo -e "${BLUE}================${NC}"
echo -e "• 流派偏好权重分析"
echo -e "• 艺术家偏好统计"
echo -e "• 播放行为特征分析"
echo -e "• 活跃度和探索性评分"
echo -e "• 音乐品味多样性分析"

echo ""
echo -e "${GREEN}🎉 推荐系统测试完成！${NC}"
