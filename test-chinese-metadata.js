const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// 使用Node.js内置的fetch（Node.js 18+）或者动态导入node-fetch
let fetch;
if (global.fetch) {
  fetch = global.fetch;
} else {
  try {
    fetch = require('node-fetch');
  } catch (err) {
    console.log('❌ fetch not available. Please use Node.js 18+ or install node-fetch');
    process.exit(1);
  }
}

/**
 * 创建包含中文元数据的测试音频文件
 */
function createChineseTestAudioFile() {
  console.log('🎵 Creating test audio file with Chinese metadata...');
  
  // 创建一个简单的WAV文件头（44字节）+ 一些音频数据
  const wavHeader = Buffer.from([
    0x52, 0x49, 0x46, 0x46, // "RIFF"
    0x24, 0x08, 0x00, 0x00, // 文件大小 - 8
    0x57, 0x41, 0x56, 0x45, // "WAVE"
    0x66, 0x6d, 0x74, 0x20, // "fmt "
    0x10, 0x00, 0x00, 0x00, // fmt chunk size
    0x01, 0x00,             // audio format (PCM)
    0x02, 0x00,             // channels (2)
    0x44, 0xac, 0x00, 0x00, // sample rate (44100)
    0x10, 0xb1, 0x02, 0x00, // byte rate
    0x04, 0x00,             // block align
    0x10, 0x00,             // bits per sample
    0x64, 0x61, 0x74, 0x61, // "data"
    0x00, 0x08, 0x00, 0x00  // data size
  ]);

  // 添加一些简单的音频数据（2048字节的静音）
  const audioData = Buffer.alloc(2048, 0);
  const testAudioBuffer = Buffer.concat([wavHeader, audioData]);

  return testAudioBuffer;
}

/**
 * 测试中文音乐元数据处理
 */
async function testChineseMetadataProcessing() {
  try {
    console.log('🎵 Testing Chinese Music Metadata Processing...\n');

    // 1. 首先登录获取token
    console.log('1. Logging in to get authentication token...');
    let token;
    
    try {
      const loginResponse = await fetch('http://localhost:3000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: 'testuser',
          password: 'password123'
        })
      });

      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        token = loginData.data.token;
        console.log('✅ Login successful');
      } else {
        throw new Error('Login failed');
      }
    } catch (loginError) {
      console.log('❌ Login failed, creating test user...');
      
      // 创建测试用户
      const registerResponse = await fetch('http://localhost:3000/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      if (registerResponse.ok) {
        console.log('✅ Test user created');
        
        // 重新登录
        const retryLoginResponse = await fetch('http://localhost:3000/api/v1/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            username: 'testuser',
            password: 'password123'
          })
        });

        const retryLoginData = await retryLoginResponse.json();
        token = retryLoginData.data.token;
        console.log('✅ Login successful after registration');
      } else {
        console.log('❌ Failed to create test user');
        return;
      }
    }

    // 2. 测试中文文件名的音乐上传
    console.log('\n2. Testing Chinese filename music upload...');
    
    const testCases = [
      {
        filename: '测试歌曲-周杰伦.wav',
        description: 'Chinese filename with artist'
      },
      {
        filename: '夜曲-钢琴版.wav',
        description: 'Chinese title with instrument'
      },
      {
        filename: '流行音乐-经典老歌.wav',
        description: 'Chinese genre description'
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n📁 Testing: ${testCase.description}`);
      console.log(`   Filename: ${testCase.filename}`);
      
      // 创建测试音频文件
      const testAudioBuffer = createChineseTestAudioFile();
      
      // 保存测试文件
      fs.writeFileSync(testCase.filename, testAudioBuffer);
      console.log(`✅ Test file created: ${testCase.filename}`);

      // 上传音乐文件
      const form = new FormData();
      form.append('music', fs.createReadStream(testCase.filename), {
        filename: testCase.filename,
        contentType: 'audio/wav'
      });

      try {
        const uploadResponse = await fetch('http://localhost:3000/api/v1/music/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            ...form.getHeaders()
          },
          body: form
        });

        const uploadResult = await uploadResponse.json();
        
        if (uploadResponse.ok) {
          console.log('✅ Upload successful!');
          console.log('📄 Metadata extracted:', {
            title: uploadResult.data.title,
            artist: uploadResult.data.artist,
            filename: testCase.filename
          });
        } else {
          console.log('❌ Upload failed:', uploadResult.message);
        }
      } catch (uploadError) {
        console.log('❌ Upload error:', uploadError.message);
      }

      // 清理测试文件
      try {
        fs.unlinkSync(testCase.filename);
        console.log('✅ Test file cleaned up');
      } catch (cleanupError) {
        console.log('⚠️  Failed to clean up test file:', cleanupError.message);
      }
    }

    // 3. 测试音频元数据服务的中文处理
    console.log('\n3. Testing AudioMetadataService Chinese encoding...');
    
    const AudioMetadataService = require('./src/services/audioMetadataService');
    
    // 测试中文编码检测
    const testTexts = [
      '周杰伦',
      '夜曲',
      '流行音乐',
      'Jay Chou',
      '邓丽君 - 月亮代表我的心'
    ];

    for (const text of testTexts) {
      const converted = AudioMetadataService.detectAndConvertEncoding(text);
      console.log(`📝 Text: "${text}" -> "${converted}"`);
    }

    // 4. 测试歌词处理
    console.log('\n4. Testing lyrics processing...');
    
    const testLyrics = [
      // LRC格式歌词
      `[00:12.00]周杰伦 - 夜曲
[00:15.30]作词：方文山
[00:18.60]作曲：周杰伦
[00:21.90]
[00:25.20]一群嗜血的蚂蚁
[00:28.50]被腐肉所吸引
[00:31.80]我面无表情看孤独的风景`,
      
      // 普通文本歌词
      `夜曲
作词：方文山
作曲：周杰伦

一群嗜血的蚂蚁
被腐肉所吸引
我面无表情看孤独的风景`
    ];

    for (let i = 0; i < testLyrics.length; i++) {
      const lyrics = testLyrics[i];
      const processed = AudioMetadataService.processLyrics(lyrics);
      
      console.log(`🎵 Lyrics test ${i + 1}:`);
      console.log(`   Type: ${processed?.type || 'null'}`);
      console.log(`   Has timestamps: ${processed?.hasTimestamps || false}`);
      console.log(`   Total lines: ${processed?.totalLines || 0}`);
      
      if (processed?.type === 'lrc' && processed.lyrics) {
        console.log(`   First lyric: ${processed.lyrics[0]?.text || 'N/A'}`);
      }
    }

    console.log('\n✅ Chinese metadata processing test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testChineseMetadataProcessing();
}

module.exports = testChineseMetadataProcessing;
