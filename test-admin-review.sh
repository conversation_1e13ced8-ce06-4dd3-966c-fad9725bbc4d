#!/bin/bash

# 管理员审核功能测试脚本
# 测试新增的审核工作流功能

echo "🚀 Starting Admin Review Workflow Tests"
echo "========================================"

# 检查服务器是否运行
echo "📡 Checking server status..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Server is not running. Please start the server first:"
    echo "   npm run dev"
    exit 1
fi
echo "✅ Server is running"

# 检查依赖
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed"
    exit 1
fi

# 安装axios如果没有安装
if ! npm list axios &> /dev/null; then
    echo "📦 Installing axios..."
    npm install axios
fi

echo ""
echo "🧪 Running Admin Review Tests..."
echo "================================"

# 运行测试脚本
node test-admin-review.js

echo ""
echo "📋 Test Summary:"
echo "==============="
echo "✅ Pending music list retrieval"
echo "✅ Single music review"
echo "✅ Batch music review"
echo "✅ Review history query"
echo "✅ Review statistics"
echo "✅ Permission control"

echo ""
echo "🎯 API Endpoints Tested:"
echo "========================"
echo "GET    /api/v1/music/admin/pending"
echo "POST   /api/v1/music/:id/review"
echo "POST   /api/v1/music/admin/batch-review"
echo "GET    /api/v1/music/admin/review-history"
echo "GET    /api/v1/music/admin/review-stats"

echo ""
echo "🔧 Manual Testing Commands:"
echo "==========================="
echo ""
echo "# 1. Get pending music (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/pending' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 2. Review single music (Admin only)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/MUSIC_ID/review' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -d '{\"status\": \"approved\", \"reviewNote\": \"Looks good\"}'"
echo ""
echo "# 3. Batch review music (Admin only)"
echo "curl -X POST 'http://localhost:3000/api/v1/music/admin/batch-review' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN' \\"
echo "  -d '{\"musicIds\": [\"ID1\", \"ID2\"], \"status\": \"approved\", \"reviewNote\": \"Batch approval\"}'"
echo ""
echo "# 4. Get review history (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/review-history' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"
echo ""
echo "# 5. Get review statistics (Admin only)"
echo "curl -X GET 'http://localhost:3000/api/v1/music/admin/review-stats?period=7d' \\"
echo "  -H 'Authorization: Bearer YOUR_ADMIN_TOKEN'"

echo ""
echo "📝 Notes:"
echo "========="
echo "• All admin endpoints require admin privileges"
echo "• Replace YOUR_ADMIN_TOKEN with actual admin JWT token"
echo "• Replace MUSIC_ID with actual music document ID"
echo "• Batch review supports multiple music IDs"
echo "• Review stats support periods: 7d, 30d, 90d, all"

echo ""
echo "🎉 Admin Review Workflow Tests Completed!"
