# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/musicdou
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# MinIO Configuration
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_USE_SSL=false
MINIO_BUCKET_MUSIC=music
MINIO_BUCKET_IMAGES=images
MINIO_BUCKET_AVATARS=avatars

# File Upload Configuration
MAX_FILE_SIZE=50MB
ALLOWED_AUDIO_FORMATS=mp3,flac,wav,aac
ALLOWED_IMAGE_FORMATS=jpg,jpeg,png,webp

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (for future use)
EMAIL_HOST=
EMAIL_PORT=587
EMAIL_USER=
EMAIL_PASS=

# External API Keys (for music platform plugins)
NETEASE_API_KEY=
QQ_MUSIC_API_KEY=
KUGOU_API_KEY=
KUWO_API_KEY=
