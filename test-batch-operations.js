#!/usr/bin/env node

/**
 * 批量操作功能测试脚本
 * 测试新增的批量操作接口
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

let adminToken = '';

/**
 * 登录获取token
 */
async function login(credentials) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials);
    return response.data.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试批量更新音乐信息
 */
async function testBatchUpdateMusic() {
  console.log('\n📝 Testing: Batch update music');
  try {
    // 先获取一些音乐ID
    const musicResponse = await axios.get(`${BASE_URL}/music`, {
      params: { limit: 3 }
    });
    
    if (musicResponse.data.data.music.length === 0) {
      console.log('ℹ️  No music found for batch update test');
      return null;
    }
    
    const musicIds = musicResponse.data.data.music.map(m => m._id);
    
    const response = await axios.post(`${BASE_URL}/music/admin/batch-update`, {
      musicIds: musicIds.slice(0, 2), // 只更新前2个
      updateData: {
        genre: 'Test Genre',
        tags: ['test', 'batch-update']
      }
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ Batch update completed successfully');
    console.log(`   Success: ${response.data.data.successCount}`);
    console.log(`   Failed: ${response.data.data.failedCount}`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Batch update failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试批量移动音乐状态
 */
async function testBatchMoveMusic() {
  console.log('\n🔄 Testing: Batch move music status');
  try {
    // 先获取一些待审核的音乐
    const pendingResponse = await axios.get(`${BASE_URL}/music/admin/pending`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { limit: 2 }
    });
    
    if (pendingResponse.data.data.music.length === 0) {
      console.log('ℹ️  No pending music found for batch move test');
      return null;
    }
    
    const musicIds = pendingResponse.data.data.music.map(m => m._id);
    
    const response = await axios.post(`${BASE_URL}/music/admin/batch-move`, {
      musicIds,
      targetStatus: 'approved',
      reason: 'Batch approval for testing'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ Batch move completed successfully');
    console.log(`   Success: ${response.data.data.successCount}`);
    console.log(`   Failed: ${response.data.data.failedCount}`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Batch move failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试批量导出音乐信息
 */
async function testBatchExportMusic() {
  console.log('\n📤 Testing: Batch export music');
  try {
    // 先获取一些音乐ID
    const musicResponse = await axios.get(`${BASE_URL}/music`, {
      params: { limit: 5 }
    });
    
    if (musicResponse.data.data.music.length === 0) {
      console.log('ℹ️  No music found for batch export test');
      return null;
    }
    
    const musicIds = musicResponse.data.data.music.map(m => m._id);
    
    // 测试JSON导出
    const jsonResponse = await axios.post(`${BASE_URL}/music/admin/batch-export`, {
      musicIds: musicIds.slice(0, 3),
      format: 'json',
      fields: ['_id', 'title', 'artist', 'album', 'genre', 'status']
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ JSON export completed successfully');
    console.log(`   Data length: ${jsonResponse.data.length} characters`);
    
    // 测试CSV导出
    const csvResponse = await axios.post(`${BASE_URL}/music/admin/batch-export`, {
      musicIds: musicIds.slice(0, 2),
      format: 'csv',
      fields: ['title', 'artist', 'album', 'genre']
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ CSV export completed successfully');
    console.log(`   Data length: ${csvResponse.data.length} characters`);
    console.log(`   Sample CSV: ${csvResponse.data.split('\\n')[0]}`);
    
    return { json: jsonResponse.data, csv: csvResponse.data };
  } catch (error) {
    console.error('❌ Batch export failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试批量操作历史
 */
async function testBatchOperationHistory() {
  console.log('\n📚 Testing: Batch operation history');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/batch-operations`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { page: 1, limit: 10 }
    });
    
    console.log('✅ Batch operation history retrieved successfully');
    console.log(`   Total operations: ${response.data.data.pagination.totalCount}`);
    
    if (response.data.data.operations.length > 0) {
      const latest = response.data.data.operations[0];
      console.log(`   Latest operation: ${latest.operationType}`);
      console.log(`   Status: ${latest.status}`);
      console.log(`   Success/Failed: ${latest.result.successCount}/${latest.result.failedCount}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Batch operation history failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试批量删除音乐（谨慎使用）
 */
async function testBatchDeleteMusic() {
  console.log('\n🗑️  Testing: Batch delete music (CAUTION)');
  try {
    // 为了安全，我们不会真正删除音乐，只是测试API结构
    console.log('ℹ️  Skipping actual deletion for safety');
    console.log('   API endpoint: POST /api/v1/music/admin/batch-delete');
    console.log('   Required params: musicIds (array), force (boolean)');
    
    return { skipped: true, reason: 'Safety precaution' };
  } catch (error) {
    console.error('❌ Batch delete test failed:', error.message);
    return null;
  }
}

/**
 * 测试权限控制
 */
async function testPermissionControl() {
  console.log('\n🔒 Testing: Permission control for batch operations');
  try {
    // 尝试不带token访问批量操作接口
    const response = await axios.post(`${BASE_URL}/music/admin/batch-update`, {
      musicIds: ['test'],
      updateData: { genre: 'test' }
    });
    
    console.log('❌ Permission control failed - should require admin token');
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      console.log('✅ Permission control working - access denied without admin token');
    } else {
      console.error('❌ Unexpected error:', error.response?.data?.message || error.message);
    }
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 Starting Batch Operations Tests\n');
  
  // 登录获取token
  console.log('🔐 Logging in as admin...');
  adminToken = await login(ADMIN_CREDENTIALS);
  
  if (!adminToken) {
    console.error('❌ Failed to get admin token. Please ensure admin user exists.');
    return;
  }
  
  console.log('✅ Admin login successful');
  
  // 运行测试
  await testBatchUpdateMusic();
  await testBatchMoveMusic();
  await testBatchExportMusic();
  await testBatchOperationHistory();
  await testBatchDeleteMusic();
  await testPermissionControl();
  
  console.log('\n🎉 Batch Operations Tests Completed!');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testBatchUpdateMusic,
  testBatchMoveMusic,
  testBatchExportMusic,
  testBatchOperationHistory,
  testBatchDeleteMusic,
  testPermissionControl
};
