#!/usr/bin/env node

/**
 * 推荐系统测试脚本
 * 测试推荐系统的各项功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';
let testUserId = '';
let testMusicId = '';

// 测试用户凭据
const testUser = {
  username: 'rec_test_user',
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

/**
 * 发送HTTP请求的辅助函数
 */
async function makeRequest(method, url, data = null, useAuth = true) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`HTTP ${error.response.status}: ${JSON.stringify(error.response.data)}`);
    }
    throw error;
  }
}

/**
 * 用户认证
 */
async function authenticateUser() {
  console.log('🔐 开始用户认证...');
  
  try {
    // 尝试登录
    const loginResult = await makeRequest('POST', '/auth/login', {
      username: testUser.username,
      password: testUser.password
    }, false);
    
    authToken = loginResult.token;
    testUserId = loginResult.user.id;
    console.log('✅ 用户登录成功');
    
  } catch (error) {
    console.log('📝 用户不存在，尝试注册...');
    
    try {
      // 注册新用户
      await makeRequest('POST', '/auth/register', testUser, false);
      console.log('✅ 用户注册成功');
      
      // 登录
      const loginResult = await makeRequest('POST', '/auth/login', {
        username: testUser.username,
        password: testUser.password
      }, false);
      
      authToken = loginResult.token;
      testUserId = loginResult.user.id;
      console.log('✅ 用户登录成功');
      
    } catch (registerError) {
      throw new Error(`认证失败: ${registerError.message}`);
    }
  }
}

/**
 * 获取测试音乐ID
 */
async function getTestMusicId() {
  console.log('🎵 获取测试音乐ID...');
  
  try {
    const musicList = await makeRequest('GET', '/music?limit=1');
    
    if (musicList.data && musicList.data.music && musicList.data.music.length > 0) {
      testMusicId = musicList.data.music[0]._id;
      console.log(`✅ 获取到测试音乐ID: ${testMusicId}`);
    } else {
      console.log('⚠️  没有找到音乐数据，某些测试可能会跳过');
    }
  } catch (error) {
    console.log(`⚠️  获取音乐数据失败: ${error.message}`);
  }
}

/**
 * 测试个性化推荐
 */
async function testPersonalizedRecommendations() {
  console.log('\n📊 测试个性化推荐...');
  
  try {
    const result = await makeRequest('GET', '/recommendations/personalized?limit=10');
    
    console.log('✅ 个性化推荐请求成功');
    console.log(`   推荐数量: ${result.data.recommendations.length}`);
    console.log(`   算法: ${result.data.metadata.algorithm}`);
    console.log(`   置信度: ${result.data.metadata.confidence}`);
    console.log(`   处理时间: ${result.data.metadata.processingTime}ms`);
    
    if (result.data.recommendations.length > 0) {
      console.log('   前3个推荐:');
      result.data.recommendations.slice(0, 3).forEach((rec, index) => {
        console.log(`     ${index + 1}. 分数: ${rec.score.toFixed(3)}, 原因: ${rec.reason}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 个性化推荐测试失败: ${error.message}`);
  }
}

/**
 * 测试相似音乐推荐
 */
async function testSimilarMusicRecommendations() {
  console.log('\n🎼 测试相似音乐推荐...');
  
  if (!testMusicId) {
    console.log('⚠️  跳过相似音乐推荐测试（没有测试音乐ID）');
    return;
  }
  
  try {
    const result = await makeRequest('GET', `/recommendations/similar/${testMusicId}?limit=5`);
    
    console.log('✅ 相似音乐推荐请求成功');
    console.log(`   推荐数量: ${result.data.recommendations.length}`);
    console.log(`   基于音乐ID: ${result.data.musicId}`);
    
    if (result.data.recommendations.length > 0) {
      console.log('   推荐列表:');
      result.data.recommendations.forEach((rec, index) => {
        console.log(`     ${index + 1}. 分数: ${rec.score.toFixed(3)}, 原因: ${rec.reason}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 相似音乐推荐测试失败: ${error.message}`);
  }
}

/**
 * 测试热门推荐
 */
async function testPopularRecommendations() {
  console.log('\n🔥 测试热门推荐...');
  
  try {
    const result = await makeRequest('GET', '/recommendations/popular?limit=8');
    
    console.log('✅ 热门推荐请求成功');
    console.log(`   推荐数量: ${result.data.recommendations.length}`);
    console.log(`   算法: ${result.data.metadata.algorithm}`);
    
    if (result.data.recommendations.length > 0) {
      console.log('   前3个热门推荐:');
      result.data.recommendations.slice(0, 3).forEach((rec, index) => {
        console.log(`     ${index + 1}. 分数: ${rec.score.toFixed(3)}, 原因: ${rec.reason}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 热门推荐测试失败: ${error.message}`);
  }
}

/**
 * 测试发现推荐
 */
async function testDiscoveryRecommendations() {
  console.log('\n🔍 测试发现推荐...');
  
  try {
    const result = await makeRequest('GET', '/recommendations/discover?limit=6');
    
    console.log('✅ 发现推荐请求成功');
    console.log(`   推荐数量: ${result.data.recommendations.length}`);
    console.log(`   算法: ${result.data.metadata.algorithm}`);
    
    if (result.data.recommendations.length > 0) {
      console.log('   发现推荐示例:');
      result.data.recommendations.slice(0, 2).forEach((rec, index) => {
        console.log(`     ${index + 1}. 分数: ${rec.score.toFixed(3)}, 原因: ${rec.reason}`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 发现推荐测试失败: ${error.message}`);
  }
}

/**
 * 测试用户偏好获取
 */
async function testGetUserPreferences() {
  console.log('\n👤 测试获取用户偏好...');
  
  try {
    const result = await makeRequest('GET', '/recommendations/preferences');
    
    console.log('✅ 获取用户偏好成功');
    
    if (result.data.genrePreferences) {
      console.log(`   流派偏好数量: ${result.data.genrePreferences.length}`);
      if (result.data.genrePreferences.length > 0) {
        console.log('   前3个流派偏好:');
        result.data.genrePreferences.slice(0, 3).forEach((pref, index) => {
          console.log(`     ${index + 1}. ${pref.genre}: 权重 ${pref.weight}, 播放 ${pref.playCount} 次`);
        });
      }
    }
    
    if (result.data.artistPreferences) {
      console.log(`   艺术家偏好数量: ${result.data.artistPreferences.length}`);
    }
    
    if (result.data.behaviorProfile) {
      console.log('   行为特征:');
      console.log(`     完成率: ${result.data.behaviorProfile.completionRate?.toFixed(1)}%`);
      console.log(`     跳过率: ${result.data.behaviorProfile.skipRate?.toFixed(1)}%`);
      console.log(`     活跃度: ${result.data.behaviorProfile.activityScore?.toFixed(1)}`);
    }
    
  } catch (error) {
    console.log(`❌ 获取用户偏好测试失败: ${error.message}`);
  }
}

/**
 * 测试推荐反馈记录
 */
async function testRecommendationFeedback() {
  console.log('\n📝 测试推荐反馈记录...');
  
  if (!testMusicId) {
    console.log('⚠️  跳过推荐反馈测试（没有测试音乐ID）');
    return;
  }
  
  try {
    const feedbackData = {
      musicId: testMusicId,
      feedbackType: 'play',
      playDuration: 120,
      isCompleted: false,
      isSkipped: false,
      dwellTime: 5000,
      explicitFeedback: 'like'
    };
    
    const result = await makeRequest('POST', '/recommendations/feedback', feedbackData);
    
    console.log('✅ 推荐反馈记录成功');
    console.log(`   消息: ${result.message}`);
    
  } catch (error) {
    console.log(`❌ 推荐反馈记录测试失败: ${error.message}`);
  }
}

/**
 * 测试行为分析刷新
 */
async function testBehaviorAnalysis() {
  console.log('\n🧠 测试行为分析刷新...');
  
  try {
    const result = await makeRequest('POST', '/recommendations/analyze-behavior', {
      timeRange: '30d'
    });
    
    console.log('✅ 行为分析刷新成功');
    
    if (result.data) {
      console.log(`   分析时间: ${result.data.analysisTime}ms`);
      console.log(`   播放历史数量: ${result.data.playHistoryCount}`);
    } else {
      console.log(`   消息: ${result.message}`);
    }
    
  } catch (error) {
    console.log(`❌ 行为分析刷新测试失败: ${error.message}`);
  }
}

/**
 * 测试推荐统计
 */
async function testRecommendationStats() {
  console.log('\n📈 测试推荐统计...');
  
  try {
    const result = await makeRequest('GET', '/recommendations/stats?timeRange=7d');
    
    console.log('✅ 获取推荐统计成功');
    console.log(`   统计数据条目: ${result.data.stats.length}`);
    
    if (result.data.stats.length > 0) {
      console.log('   统计详情:');
      result.data.stats.forEach(stat => {
        console.log(`     ${stat._id}: ${stat.count} 次, 平均响应时间: ${stat.avgResponseTime?.toFixed(1)}ms`);
      });
    }
    
  } catch (error) {
    console.log(`❌ 推荐统计测试失败: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始推荐系统测试\n');
  
  try {
    // 用户认证
    await authenticateUser();
    
    // 获取测试数据
    await getTestMusicId();
    
    // 运行各项测试
    await testPersonalizedRecommendations();
    await testSimilarMusicRecommendations();
    await testPopularRecommendations();
    await testDiscoveryRecommendations();
    await testGetUserPreferences();
    await testRecommendationFeedback();
    await testBehaviorAnalysis();
    await testRecommendationStats();
    
    console.log('\n🎉 推荐系统测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
