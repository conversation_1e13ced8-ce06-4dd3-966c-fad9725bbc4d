# MusicDou API 文档

## 📋 API 概览

MusicDou 是一个完整的音乐流媒体平台，提供用户管理、音乐上传、歌单管理等功能。

**Base URL**: `http://localhost:3000/api/v1`

## 🔐 认证

大部分API需要JWT认证。在请求头中包含：
```
Authorization: Bearer <your-jwt-token>
```

## 📚 API 接口列表

### 1. 用户认证 (Auth)

#### 用户注册
- **POST** `/auth/register`
- **Body**: `{ username, email, password }`
- **Response**: 用户信息和JWT token

#### 用户登录
- **POST** `/auth/login`
- **Body**: `{ identifier, password }`
- **Response**: 用户信息和JWT token

#### 获取当前用户信息
- **GET** `/auth/me`
- **Auth**: Required
- **Response**: 当前用户详细信息

#### 更新用户资料
- **PUT** `/auth/profile`
- **Auth**: Required
- **Body**: `{ username, email, bio, avatar }`
- **Response**: 更新后的用户信息

### 2. 文件上传 (Upload)

#### 上传头像
- **POST** `/upload/avatar`
- **Auth**: Required
- **Body**: FormData with `avatar` file
- **Response**: 上传结果和文件信息

#### 上传封面
- **POST** `/upload/cover`
- **Auth**: Required
- **Body**: FormData with `cover` file
- **Response**: 上传结果和文件信息

#### 获取上传信息
- **GET** `/upload/info`
- **Response**: 上传限制和配置信息

### 3. 音乐管理 (Music)

#### 获取音乐列表
- **GET** `/music`
- **Query**: `page, limit, search, genre, artist, status`
- **Response**: 音乐列表和分页信息

#### 获取音乐详情
- **GET** `/music/:id`
- **Response**: 音乐详细信息

#### 上传音乐
- **POST** `/music-upload`
- **Auth**: Required
- **Body**: FormData with music file and metadata
- **Response**: 上传结果

#### 更新音乐信息
- **PUT** `/music/:id`
- **Auth**: Required (Admin)
- **Body**: 音乐元数据
- **Response**: 更新后的音乐信息

#### 删除音乐
- **DELETE** `/music/:id`
- **Auth**: Required (Admin)
- **Response**: 删除确认

#### 音乐流媒体
- **GET** `/music/:id/stream`
- **Query**: `quality` (optional)
- **Response**: 音频流

#### 批量操作
- **POST** `/music/batch`
- **Auth**: Required (Admin)
- **Body**: `{ action, musicIds }`
- **Response**: 批量操作结果

### 4. 歌单管理 (Playlists)

#### 获取歌单列表
- **GET** `/playlists`
- **Query**: `page, limit, search, category, tags, publicOnly, userId`
- **Response**: 歌单列表和分页信息

#### 创建歌单
- **POST** `/playlists`
- **Auth**: Required
- **Body**: `{ name, description, isPublic, tags, category }`
- **Response**: 创建的歌单信息

#### 获取歌单详情
- **GET** `/playlists/:id`
- **Response**: 歌单详细信息（包含歌曲列表）

#### 更新歌单
- **PUT** `/playlists/:id`
- **Auth**: Required (Owner)
- **Body**: `{ name, description, isPublic, tags, category }`
- **Response**: 更新后的歌单信息

#### 删除歌单
- **DELETE** `/playlists/:id`
- **Auth**: Required (Owner)
- **Response**: 删除确认

#### 获取热门歌单
- **GET** `/playlists/popular`
- **Query**: `page, limit, timeRange`
- **Response**: 热门歌单列表

#### 获取用户歌单
- **GET** `/playlists/user/:userId`
- **Query**: `page, limit, includePrivate`
- **Response**: 用户的歌单列表

#### 获取收藏的歌单
- **GET** `/playlists/favorites`
- **Auth**: Required
- **Query**: `page, limit`
- **Response**: 用户收藏的歌单列表

### 5. 歌单歌曲管理

#### 添加歌曲到歌单
- **POST** `/playlists/:id/songs`
- **Auth**: Required (Owner)
- **Body**: `{ musicId, order }`
- **Response**: 更新后的歌单

#### 从歌单移除歌曲
- **DELETE** `/playlists/:id/songs/:musicId`
- **Auth**: Required (Owner)
- **Response**: 更新后的歌单

#### 重新排序歌曲
- **PUT** `/playlists/:id/songs/reorder`
- **Auth**: Required (Owner)
- **Body**: `{ songOrders: [{ musicId, order }] }`
- **Response**: 重新排序后的歌单

#### 批量添加歌曲
- **POST** `/playlists/:id/songs/batch`
- **Auth**: Required (Owner)
- **Body**: `{ musicIds: [musicId1, musicId2, ...] }`
- **Response**: 批量添加结果

### 6. 歌单收藏功能

#### 收藏歌单
- **POST** `/playlists/:id/favorite`
- **Auth**: Required
- **Response**: 收藏结果

#### 取消收藏歌单
- **DELETE** `/playlists/:id/favorite`
- **Auth**: Required
- **Response**: 取消收藏结果

### 7. 歌单封面管理

#### 上传歌单封面
- **POST** `/playlists/:id/cover`
- **Auth**: Required (Owner)
- **Body**: FormData with `cover` file
- **Response**: 上传结果和封面URL

#### 删除歌单封面
- **DELETE** `/playlists/:id/cover`
- **Auth**: Required (Owner)
- **Response**: 删除确认

### 8. 积分系统 (Points)

#### 获取用户积分
- **GET** `/points`
- **Auth**: Required
- **Response**: 用户积分信息

#### 获取积分历史
- **GET** `/points/history`
- **Auth**: Required
- **Query**: `page, limit`
- **Response**: 积分变动历史

### 9. 音频质量管理

#### 获取音频质量信息
- **GET** `/audio-quality/:musicId`
- **Response**: 音频质量详细信息

#### 更新音频质量
- **PUT** `/audio-quality/:musicId`
- **Auth**: Required (Admin)
- **Body**: 质量参数
- **Response**: 更新结果

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "错误描述"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

## 🔒 权限说明

- **Public**: 无需认证
- **User**: 需要用户认证
- **Owner**: 需要资源所有者权限
- **Admin**: 需要管理员权限

## 📝 状态码

- `200` - 成功
- `201` - 创建成功
- `400` - 请求错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `500` - 服务器错误

## 🧪 测试

项目包含完整的测试套件：
- 基础功能测试
- 权限控制测试
- 错误处理测试
- 集成测试

运行测试：
```bash
node test-playlists.js
node test-playlist-songs.js
node test-playlist-favorites.js
node test-playlist-cover.js
node test-playlist-system-complete.js
```

---

**最后更新**: 2025-01-31  
**API版本**: v1  
**文档版本**: 1.0
