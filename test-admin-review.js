#!/usr/bin/env node

/**
 * 管理员审核功能测试脚本
 * 测试新增的审核工作流功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';

// 测试用户凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

const USER_CREDENTIALS = {
  username: 'testuser',
  password: 'password123'
};

let adminToken = '';
let userToken = '';

/**
 * 登录获取token
 */
async function login(credentials) {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials);
    return response.data.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试获取待审核音乐列表
 */
async function testGetPendingMusic() {
  console.log('\n📋 Testing: Get pending music list');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/pending`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { page: 1, limit: 10 }
    });
    
    console.log('✅ Pending music list retrieved successfully');
    console.log(`   Found ${response.data.data.pagination.totalCount} pending music`);
    
    return response.data.data.music;
  } catch (error) {
    console.error('❌ Failed to get pending music:', error.response?.data?.message || error.message);
    return [];
  }
}

/**
 * 测试单个音乐审核
 */
async function testReviewMusic(musicId) {
  console.log('\n✅ Testing: Review single music');
  try {
    const response = await axios.post(`${BASE_URL}/music/${musicId}/review`, {
      status: 'approved',
      reviewNote: 'Test approval by admin'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ Music reviewed successfully');
    console.log(`   Music: ${response.data.data.title} by ${response.data.data.artist}`);
    console.log(`   Status: ${response.data.data.status}`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to review music:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试批量审核
 */
async function testBatchReview(musicIds) {
  console.log('\n📦 Testing: Batch review music');
  try {
    const response = await axios.post(`${BASE_URL}/music/admin/batch-review`, {
      musicIds: musicIds.slice(0, 3), // 只测试前3个
      status: 'approved',
      reviewNote: 'Batch approval test'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ Batch review completed successfully');
    console.log(`   Success: ${response.data.data.successCount}`);
    console.log(`   Failed: ${response.data.data.failedCount}`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to batch review:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试审核历史
 */
async function testGetReviewHistory() {
  console.log('\n📚 Testing: Get review history');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/review-history`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { page: 1, limit: 10 }
    });
    
    console.log('✅ Review history retrieved successfully');
    console.log(`   Total reviews: ${response.data.data.pagination.totalCount}`);
    
    if (response.data.data.reviews.length > 0) {
      const latest = response.data.data.reviews[0];
      console.log(`   Latest review: ${latest.title} - ${latest.status}`);
      console.log(`   Reviewed by: ${latest.reviewedBy.username}`);
    }
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to get review history:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试审核统计
 */
async function testGetReviewStats() {
  console.log('\n📊 Testing: Get review statistics');
  try {
    const response = await axios.get(`${BASE_URL}/music/admin/review-stats`, {
      headers: { Authorization: `Bearer ${adminToken}` },
      params: { period: '7d' }
    });
    
    console.log('✅ Review statistics retrieved successfully');
    console.log(`   Period: ${response.data.data.period}`);
    console.log(`   Total reviewed: ${response.data.data.summary.totalReviewed}`);
    console.log(`   Approved: ${response.data.data.summary.approvedCount}`);
    console.log(`   Rejected: ${response.data.data.summary.rejectedCount}`);
    console.log(`   Approval rate: ${response.data.data.summary.approvalRate}%`);
    
    return response.data.data;
  } catch (error) {
    console.error('❌ Failed to get review stats:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 测试权限控制
 */
async function testPermissionControl() {
  console.log('\n🔒 Testing: Permission control');
  try {
    // 尝试用普通用户访问管理员接口
    const response = await axios.get(`${BASE_URL}/music/admin/pending`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    
    console.log('❌ Permission control failed - user should not access admin endpoints');
  } catch (error) {
    if (error.response?.status === 403) {
      console.log('✅ Permission control working - access denied for non-admin user');
    } else {
      console.error('❌ Unexpected error:', error.response?.data?.message || error.message);
    }
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 Starting Admin Review Workflow Tests\n');
  
  // 登录获取token
  console.log('🔐 Logging in...');
  adminToken = await login(ADMIN_CREDENTIALS);
  userToken = await login(USER_CREDENTIALS);
  
  if (!adminToken) {
    console.error('❌ Failed to get admin token. Please ensure admin user exists.');
    return;
  }
  
  if (!userToken) {
    console.error('❌ Failed to get user token. Please ensure test user exists.');
    return;
  }
  
  console.log('✅ Login successful');
  
  // 运行测试
  const pendingMusic = await testGetPendingMusic();
  
  if (pendingMusic.length > 0) {
    await testReviewMusic(pendingMusic[0]._id);
    
    if (pendingMusic.length > 1) {
      const musicIds = pendingMusic.slice(1).map(music => music._id);
      await testBatchReview(musicIds);
    }
  } else {
    console.log('ℹ️  No pending music found for review tests');
  }
  
  await testGetReviewHistory();
  await testGetReviewStats();
  await testPermissionControl();
  
  console.log('\n🎉 Admin Review Workflow Tests Completed!');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testGetPendingMusic,
  testReviewMusic,
  testBatchReview,
  testGetReviewHistory,
  testGetReviewStats
};
