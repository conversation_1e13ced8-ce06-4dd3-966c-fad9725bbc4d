#!/bin/bash

# MusicDou Docker 开发环境管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker 未运行，请启动 Docker Desktop"
        exit 1
    fi
}

# 启动服务
start_services() {
    print_message "启动 MusicDou 开发环境..."
    check_docker
    docker-compose up -d
    print_message "等待服务启动..."
    sleep 10
    docker-compose ps
}

# 停止服务
stop_services() {
    print_message "停止 MusicDou 开发环境..."
    docker-compose down
}

# 重启服务
restart_services() {
    print_message "重启 MusicDou 开发环境..."
    stop_services
    start_services
}

# 查看日志
show_logs() {
    if [ -z "$1" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$1"
    fi
}

# 查看服务状态
show_status() {
    print_message "MusicDou 服务状态:"
    docker-compose ps
}

# 清理数据
clean_data() {
    print_warning "这将删除所有数据库数据，确定要继续吗? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_message "停止服务并清理数据..."
        docker-compose down -v
        docker volume prune -f
        print_message "数据清理完成"
    else
        print_message "操作已取消"
    fi
}

# 进入容器
enter_container() {
    if [ -z "$1" ]; then
        print_error "请指定容器名称: mongodb, redis, minio"
        exit 1
    fi
    
    case "$1" in
        mongodb|mongo)
            docker-compose exec mongodb mongosh -u admin -p musicdou123 --authenticationDatabase admin
            ;;
        redis)
            docker-compose exec redis redis-cli -a musicdou123
            ;;
        minio)
            docker-compose exec minio sh
            ;;
        *)
            print_error "未知的容器名称: $1"
            print_error "可用的容器: mongodb, redis, minio"
            exit 1
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "MusicDou Docker 开发环境管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看服务状态"
    echo "  logs      查看所有服务日志"
    echo "  logs <service>  查看指定服务日志"
    echo "  clean     清理所有数据"
    echo "  enter <container>  进入指定容器"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  $0 logs mongodb"
    echo "  $0 enter redis"
}

# 主逻辑
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    clean)
        clean_data
        ;;
    enter)
        enter_container "$2"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "未知命令: $1"
        show_help
        exit 1
        ;;
esac
