const mongoose = require('mongoose');
require('dotenv').config();

// 连接数据库
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/musicdou');

// 导入模型
const Music = require('./src/models/Music');
const User = require('./src/models/User');

async function createTestMusic() {
  try {
    console.log('🎵 Creating test music data...');
    
    // 获取测试用户
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`✅ Found test user: ${testUser.username} (${testUser._id})`);
    
    // 检查是否已有测试音乐
    const existingMusic = await Music.countDocuments();
    console.log(`📊 Current music count: ${existingMusic}`);

    if (existingMusic >= 5) {
      console.log('✅ Already have enough test music');
      const musicList = await Music.find().limit(5);
      console.log('📋 Existing music:');
      musicList.forEach((music, index) => {
        console.log(`   ${index + 1}. ${music.title} by ${music.artist} (${music._id})`);
      });
      return;
    }
    
    // 创建测试音乐数据
    const testMusicData = [
      {
        title: '夜曲',
        artist: '周杰伦',
        album: '十一月的萧邦',
        genre: 'Pop',
        year: 2005,
        duration: 237,
        bitrate: 320,
        sampleRate: 44100,
        channels: 2,
        fileName: 'test-music-1.mp3',
        originalName: '夜曲.mp3',
        fileFormat: 'mp3',
        fileSize: 9500000,
        mimeType: 'audio/mpeg',
        bucket: 'music',
        filePath: 'test/test-music-1.mp3',
        etag: 'test-etag-1',
        uploadedBy: testUser._id,
        quality: 'super',
        status: 'approved'
      },
      {
        title: '青花瓷',
        artist: '周杰伦',
        album: '我很忙',
        genre: 'Pop',
        year: 2007,
        duration: 228,
        bitrate: 320,
        sampleRate: 44100,
        channels: 2,
        fileName: 'test-music-2.mp3',
        originalName: '青花瓷.mp3',
        fileFormat: 'mp3',
        fileSize: 9100000,
        mimeType: 'audio/mpeg',
        bucket: 'music',
        filePath: 'test/test-music-2.mp3',
        etag: 'test-etag-2',
        uploadedBy: testUser._id,
        quality: 'super',
        status: 'approved'
      },
      {
        title: '稻香',
        artist: '周杰伦',
        album: '魔杰座',
        genre: 'Pop',
        year: 2008,
        duration: 223,
        bitrate: 320,
        sampleRate: 44100,
        channels: 2,
        fileName: 'test-music-3.mp3',
        originalName: '稻香.mp3',
        fileFormat: 'mp3',
        fileSize: 8900000,
        mimeType: 'audio/mpeg',
        bucket: 'music',
        filePath: 'test/test-music-3.mp3',
        etag: 'test-etag-3',
        uploadedBy: testUser._id,
        quality: 'super',
        status: 'approved'
      },
      {
        title: '告白气球',
        artist: '周杰伦',
        album: '周杰伦的床边故事',
        genre: 'Pop',
        year: 2016,
        duration: 203,
        bitrate: 320,
        sampleRate: 44100,
        channels: 2,
        fileName: 'test-music-4.mp3',
        originalName: '告白气球.mp3',
        fileFormat: 'mp3',
        fileSize: 8100000,
        mimeType: 'audio/mpeg',
        bucket: 'music',
        filePath: 'test/test-music-4.mp3',
        etag: 'test-etag-4',
        uploadedBy: testUser._id,
        quality: 'super',
        status: 'approved'
      },
      {
        title: '等你下课',
        artist: '周杰伦',
        album: '等你下课',
        genre: 'Pop',
        year: 2018,
        duration: 271,
        bitrate: 320,
        sampleRate: 44100,
        channels: 2,
        fileName: 'test-music-5.mp3',
        originalName: '等你下课.mp3',
        fileFormat: 'mp3',
        fileSize: 10800000,
        mimeType: 'audio/mpeg',
        bucket: 'music',
        filePath: 'test/test-music-5.mp3',
        etag: 'test-etag-5',
        uploadedBy: testUser._id,
        quality: 'super',
        status: 'approved'
      }
    ];
    
    // 批量插入音乐数据
    const createdMusic = await Music.insertMany(testMusicData);
    
    console.log(`✅ Created ${createdMusic.length} test music records:`);
    createdMusic.forEach((music, index) => {
      console.log(`   ${index + 1}. ${music.title} by ${music.artist} (${music._id})`);
    });
    
    console.log('\n🎉 Test music data created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating test music:', error);
  } finally {
    mongoose.connection.close();
  }
}

// 运行脚本
createTestMusic();
