const AudioMetadataService = require('./src/services/audioMetadataService');

/**
 * 测试音频元数据服务的各项功能
 */
async function testMetadataService() {
  console.log('🎵 Testing AudioMetadataService functionality...\n');

  try {
    // 1. 测试中文编码检测和转换
    console.log('1. Testing Chinese encoding detection and conversion...');
    
    const testTexts = [
      '周杰伦',
      '夜曲',
      '流行音乐',
      '<PERSON> Chou',
      '邓丽君 - 月亮代表我的心',
      '张学友',
      '王菲',
      '陈奕迅',
      '林俊杰'
    ];

    for (const text of testTexts) {
      const converted = AudioMetadataService.detectAndConvertEncoding(text);
      console.log(`📝 "${text}" -> "${converted}"`);
    }

    // 2. 测试歌词处理功能
    console.log('\n2. Testing lyrics processing...');
    
    // 测试LRC格式歌词
    const lrcLyrics = `[ti:夜曲]
[ar:周杰伦]
[al:十一月的萧邦]
[by:方文山]
[00:12.00]周杰伦 - 夜曲
[00:15.30]作词：方文山
[00:18.60]作曲：周杰伦
[00:21.90]
[00:25.20]一群嗜血的蚂蚁
[00:28.50]被腐肉所吸引
[00:31.80]我面无表情看孤独的风景
[00:35.10]失去你爱恨开始分明
[00:38.40]失去你还有什么事好关心
[00:41.70]当鸽子不再象征和平
[00:45.00]我终于被提醒`;

    console.log('🎵 Testing LRC format lyrics...');
    const processedLrc = AudioMetadataService.processLyrics(lrcLyrics);
    
    if (processedLrc) {
      console.log(`   Type: ${processedLrc.type}`);
      console.log(`   Has timestamps: ${processedLrc.hasTimestamps}`);
      console.log(`   Total lines: ${processedLrc.totalLines}`);
      console.log(`   Metadata:`, processedLrc.metadata);
      
      if (processedLrc.lyrics && processedLrc.lyrics.length > 0) {
        console.log(`   First lyric: [${processedLrc.lyrics[0].timeFormatted}] ${processedLrc.lyrics[0].text}`);
        console.log(`   Second lyric: [${processedLrc.lyrics[1]?.timeFormatted}] ${processedLrc.lyrics[1]?.text}`);
      }
    }

    // 测试普通文本歌词
    const textLyrics = `夜曲
作词：方文山
作曲：周杰伦

一群嗜血的蚂蚁
被腐肉所吸引
我面无表情看孤独的风景
失去你爱恨开始分明
失去你还有什么事好关心
当鸽子不再象征和平
我终于被提醒`;

    console.log('\n🎵 Testing plain text lyrics...');
    const processedText = AudioMetadataService.processLyrics(textLyrics);
    
    if (processedText) {
      console.log(`   Type: ${processedText.type}`);
      console.log(`   Has timestamps: ${processedText.hasTimestamps}`);
      console.log(`   Total lines: ${processedText.totalLines}`);
      console.log(`   First line: ${processedText.lines?.[0]}`);
    }

    // 3. 测试音频格式验证
    console.log('\n3. Testing audio format validation...');
    
    const testFiles = [
      { name: '测试歌曲.mp3', mime: 'audio/mpeg' },
      { name: '夜曲.flac', mime: 'audio/flac' },
      { name: '流行音乐.wav', mime: 'audio/wav' },
      { name: '经典老歌.aac', mime: 'audio/aac' },
      { name: 'test.txt', mime: 'text/plain' }
    ];

    for (const file of testFiles) {
      const validation = AudioMetadataService.validateAudioFile(file.name, file.mime);
      console.log(`📁 ${file.name} (${file.mime}): ${validation.valid ? '✅ Valid' : '❌ Invalid'}`);
      if (!validation.valid) {
        console.log(`   Error: ${validation.error}`);
      } else {
        console.log(`   Format: ${validation.format}`);
      }
    }

    // 4. 测试元数据清理和标准化
    console.log('\n4. Testing metadata cleaning and standardization...');
    
    const testMetadata = {
      title: '  夜曲  ',
      artist: '周杰伦;王力宏,林俊杰',
      album: '十一月的萧邦',
      genre: 'pop',
      year: 2005,
      duration: 237.5,
      bitrate: 320,
      sampleRate: 44100,
      channels: 2,
      lyrics: lrcLyrics,
      comment: '这是一首经典的中文流行歌曲',
      rating: 5,
      bpm: 120
    };

    const cleaned = AudioMetadataService.cleanMetadata(testMetadata, '夜曲-周杰伦.mp3');
    
    console.log('🧹 Cleaned metadata:');
    console.log(`   Title: "${cleaned.title}"`);
    console.log(`   Artist: "${cleaned.artist}"`);
    console.log(`   Album: "${cleaned.album}"`);
    console.log(`   Genre: "${cleaned.genre}"`);
    console.log(`   Year: ${cleaned.year}`);
    console.log(`   Has processed lyrics: ${cleaned.processedLyrics ? 'Yes' : 'No'}`);
    console.log(`   Lyrics type: ${cleaned.processedLyrics?.type}`);
    console.log(`   Validation errors: ${cleaned.validation?.errors?.length || 0}`);
    console.log(`   Validation warnings: ${cleaned.validation?.warnings?.length || 0}`);

    // 5. 测试质量等级判断
    console.log('\n5. Testing quality level determination...');
    
    const qualityTests = [
      { format: 'mp3', bitrate: 128, lossless: false },
      { format: 'mp3', bitrate: 192, lossless: false },
      { format: 'mp3', bitrate: 320, lossless: false },
      { format: 'flac', bitrate: 1000, lossless: true },
      { format: 'wav', bitrate: 1411, lossless: true }
    ];

    for (const test of qualityTests) {
      const quality = AudioMetadataService.determineQuality(test.format, test.bitrate, test.lossless);
      console.log(`🎧 ${test.format.toUpperCase()} ${test.bitrate}kbps ${test.lossless ? '(Lossless)' : ''}: ${quality}`);
    }

    console.log('\n✅ AudioMetadataService test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testMetadataService();
}

module.exports = testMetadataService;
