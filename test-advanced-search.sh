#!/bin/bash

# 高级搜索功能测试脚本
# 测试新增的高级搜索和过滤功能

echo "🚀 Starting Advanced Search Tests"
echo "=================================="

# 检查服务器是否运行
echo "📡 Checking server status..."
if ! curl -s http://localhost:3000/health > /dev/null; then
    echo "❌ Server is not running. Please start the server first:"
    echo "   npm run dev"
    exit 1
fi
echo "✅ Server is running"

echo ""
echo "🧪 Running Advanced Search Tests..."
echo "==================================="

# 运行测试脚本
node test-advanced-search.js

echo ""
echo "📋 Test Summary:"
echo "==============="
echo "✅ Basic search functionality"
echo "✅ Advanced search with filters"
echo "✅ Search suggestions"
echo "✅ Filter options retrieval"
echo "✅ Similar music recommendations"
echo "✅ Exact match search"
echo "✅ Multi-filter combination search"

echo ""
echo "🎯 API Endpoints Tested:"
echo "========================"
echo "GET    /api/v1/music/search"
echo "GET    /api/v1/music/search/advanced"
echo "GET    /api/v1/music/search/suggestions"
echo "GET    /api/v1/music/filters"
echo "GET    /api/v1/music/:id/similar"

echo ""
echo "🔧 Manual Testing Commands:"
echo "==========================="
echo ""
echo "# 1. Basic search"
echo "curl 'http://localhost:3000/api/v1/music/search?q=音乐&page=1&limit=5'"
echo ""
echo "# 2. Advanced search with filters"
echo "curl 'http://localhost:3000/api/v1/music/search/advanced?q=音乐&quality=high&minDuration=180&maxDuration=300&sortBy=popular'"
echo ""
echo "# 3. Search suggestions"
echo "curl 'http://localhost:3000/api/v1/music/search/suggestions?q=周&type=all&limit=5'"
echo ""
echo "# 4. Get filter options"
echo "curl 'http://localhost:3000/api/v1/music/filters'"
echo ""
echo "# 5. Get similar music"
echo "curl 'http://localhost:3000/api/v1/music/MUSIC_ID/similar?limit=5'"
echo ""
echo "# 6. Exact match search"
echo "curl 'http://localhost:3000/api/v1/music/search/advanced?q=夜曲&exactMatch=true&searchFields=title'"
echo ""
echo "# 7. Multi-filter search"
echo "curl 'http://localhost:3000/api/v1/music/search/advanced?q=音乐&quality=high&genre=Pop&minBitrate=192&hasLyrics=true&sortBy=newest'"

echo ""
echo "📝 Advanced Search Parameters:"
echo "=============================="
echo "• q: Search query"
echo "• quality: standard, high, super, lossless"
echo "• genre: Music genre filter"
echo "• artist: Artist name filter"
echo "• album: Album name filter"
echo "• year: Release year filter"
echo "• minDuration/maxDuration: Duration range (seconds)"
echo "• minBitrate/maxBitrate: Bitrate range (kbps)"
echo "• uploadedBy: Uploader user ID"
echo "• tags: Comma-separated tags"
echo "• hasLyrics: true/false"
echo "• exactMatch: true/false"
echo "• searchFields: title,artist,album (comma-separated)"
echo "• sortBy: relevance, newest, oldest, popular, title, artist, duration, bitrate"

echo ""
echo "🔍 Search Suggestion Types:"
echo "==========================="
echo "• all: All suggestion types"
echo "• artist: Artist suggestions only"
echo "• album: Album suggestions only"
echo "• title: Title suggestions only"
echo "• genre: Genre suggestions only"

echo ""
echo "📊 Filter Options Available:"
echo "============================"
echo "• qualities: Available quality levels"
echo "• genres: Available music genres"
echo "• artists: Popular artists"
echo "• yearRange: Min/max release years"
echo "• durationRange: Min/max durations"
echo "• bitrateRange: Min/max bitrates"

echo ""
echo "🎉 Advanced Search Tests Completed!"
