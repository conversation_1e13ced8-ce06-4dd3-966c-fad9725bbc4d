#!/usr/bin/env node

/**
 * 测试编辑评论功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';
let authToken = '';
let testUserId = '';
let testMusicId = '';
let testCommentId = '';

// 测试用户数据
const testUser = {
  username: 'editcommenter',
  email: '<EMAIL>',
  password: 'password123'
};

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

// 添加请求拦截器
api.interceptors.request.use(config => {
  // 只有在没有显式设置Authorization头时才使用全局token
  if (authToken && !config.headers.Authorization) {
    config.headers.Authorization = `Bearer ${authToken}`;
  }
  return config;
});

/**
 * 用户设置
 */
async function setupUser() {
  console.log('🔧 设置测试用户...');
  
  try {
    // 尝试注册
    await api.post('/auth/register', testUser);
    console.log('✅ 用户注册成功');
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('⚠️  用户已存在');
    } else {
      throw error;
    }
  }
  
  // 登录
  const loginResponse = await api.post('/auth/login', {
    identifier: testUser.username,
    password: testUser.password
  });
  
  authToken = loginResponse.data.data.token;
  testUserId = loginResponse.data.data.user._id;
  console.log('✅ 用户登录成功');
}

/**
 * 获取测试音乐
 */
async function getTestMusic() {
  console.log('🎵 获取测试音乐...');
  
  const response = await api.get('/music?limit=1');
  if (response.data.data.music && response.data.data.music.length > 0) {
    testMusicId = response.data.data.music[0]._id;
    console.log('✅ 获取到测试音乐ID:', testMusicId);
  } else {
    throw new Error('No music found for testing');
  }
}

/**
 * 创建测试评论
 */
async function createTestComment() {
  console.log('💬 创建测试评论...');
  
  const commentData = {
    content: '这是一个测试评论，用于测试编辑功能。',
    musicId: testMusicId
  };
  
  const response = await api.post('/comments', commentData);
  testCommentId = response.data.data._id;
  
  console.log('✅ 评论创建成功');
  console.log('   评论ID:', testCommentId);
  console.log('   评论内容:', response.data.data.content);
  console.log('   评论者:', response.data.data.author.username);
  console.log('   是否可编辑:', response.data.data.canEdit);
}

/**
 * 测试编辑评论
 */
async function testEditComment() {
  console.log('✏️  测试编辑评论...');
  
  const editData = {
    content: '这是一个测试评论，用于测试编辑功能。[已编辑]',
    reason: '添加编辑标记'
  };
  
  try {
    const response = await api.put(`/comments/${testCommentId}`, editData);
    
    console.log('✅ 评论编辑成功！');
    console.log('   新内容:', response.data.data.content);
    console.log('   是否已编辑:', response.data.data.isEdited);
    console.log('   编辑历史数:', response.data.data.editHistory.length);
    
    if (response.data.data.editHistory.length > 0) {
      console.log('   编辑历史:');
      response.data.data.editHistory.forEach((edit, index) => {
        console.log(`     ${index + 1}. 原内容: "${edit.content}"`);
        console.log(`        编辑时间: ${edit.editedAt}`);
        console.log(`        编辑原因: ${edit.reason || '无'}`);
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ 评论编辑失败:', error.response?.data || error.message);
    return false;
  }
}

/**
 * 测试权限检查 - 尝试编辑其他用户的评论
 */
async function testPermissionCheck() {
  console.log('🔒 测试权限检查...');
  
  // 创建另一个用户
  const otherUser = {
    username: 'otheruser',
    email: '<EMAIL>',
    password: 'password123'
  };
  
  try {
    await api.post('/auth/register', otherUser);
  } catch (error) {
    // 用户可能已存在，忽略错误
  }
  
  // 登录为另一个用户
  const loginResponse = await api.post('/auth/login', {
    identifier: otherUser.username,
    password: otherUser.password
  });
  
  const otherToken = loginResponse.data.data.token;
  
  // 尝试编辑原用户的评论
  try {
    await api.put(`/comments/${testCommentId}`, {
      content: '尝试编辑其他用户的评论',
      reason: '权限测试'
    }, {
      headers: {
        Authorization: `Bearer ${otherToken}`
      }
    });
    
    console.log('❌ 权限检查失败：不应该允许编辑其他用户的评论');
    return false;
  } catch (error) {
    if (error.response?.status === 403) {
      console.log('✅ 权限检查正常：正确阻止了编辑其他用户的评论');
      console.log('   错误信息:', error.response.data.message);
      return true;
    } else {
      console.log('❌ 权限检查异常:', error.response?.data || error.message);
      return false;
    }
  }
}

/**
 * 测试时间限制
 */
async function testTimeLimit() {
  console.log('⏰ 测试编辑时间限制...');
  
  // 恢复原用户token
  api.defaults.headers.Authorization = `Bearer ${authToken}`;
  
  // 获取评论详情
  const response = await api.get(`/comments/${testCommentId}`);
  const comment = response.data.data;
  
  console.log('   评论创建时间:', comment.createdAt);
  console.log('   当前时间:', new Date().toISOString());
  console.log('   是否可编辑:', comment.canEdit);
  
  const createdTime = new Date(comment.createdAt);
  const currentTime = new Date();
  const timeDiff = (currentTime - createdTime) / 1000 / 60; // 分钟
  
  console.log(`   时间差: ${timeDiff.toFixed(2)} 分钟`);
  
  if (timeDiff < 30) {
    console.log('✅ 时间限制检查：评论在30分钟内，应该可以编辑');
  } else {
    console.log('⚠️  时间限制检查：评论超过30分钟，可能无法编辑');
  }
}

/**
 * 运行所有测试
 */
async function runTests() {
  console.log('🚀 开始编辑评论功能测试...\n');
  
  try {
    await setupUser();
    await getTestMusic();
    await createTestComment();
    
    console.log('');
    const editSuccess = await testEditComment();
    
    console.log('');
    const permissionSuccess = await testPermissionCheck();
    
    console.log('');
    await testTimeLimit();
    
    console.log('\n🎉 测试完成！');
    console.log('\n📊 测试结果:');
    console.log(`✅ 编辑功能: ${editSuccess ? '正常' : '失败'}`);
    console.log(`✅ 权限检查: ${permissionSuccess ? '正常' : '失败'}`);
    
    if (editSuccess && permissionSuccess) {
      console.log('\n🎊 所有测试通过！编辑评论功能工作正常。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查问题。');
    }
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
