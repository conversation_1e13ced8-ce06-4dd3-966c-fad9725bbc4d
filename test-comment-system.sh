#!/bin/bash

# 音乐评论系统测试脚本
# 用于测试评论系统的各项功能

echo "🎵 MusicDou 音乐评论系统测试"
echo "================================"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查服务器是否运行
echo "🔍 检查服务器状态..."
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行，请先启动服务器: npm run dev"
    exit 1
fi

# 检查Docker服务是否运行
echo "🐳 检查Docker服务状态..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ Docker服务运行正常"
else
    echo "⚠️  Docker服务可能未运行，请检查: npm run docker:status"
fi

echo ""
echo "🚀 开始运行评论系统测试..."
echo ""

# 运行测试脚本
node test-comment-system.js

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 评论系统测试完成！"
    echo ""
    echo "📋 可用的API接口:"
    echo "POST   /api/v1/comments                    - 发布评论"
    echo "GET    /api/v1/comments/music/:musicId    - 获取音乐评论"
    echo "GET    /api/v1/comments/:id               - 获取评论详情"
    echo "PUT    /api/v1/comments/:id               - 编辑评论"
    echo "DELETE /api/v1/comments/:id               - 删除评论"
    echo "GET    /api/v1/comments/:id/replies       - 获取评论回复"
    echo "POST   /api/v1/comments/:id/like          - 点赞评论"
    echo "DELETE /api/v1/comments/:id/like          - 取消点赞"
    echo "POST   /api/v1/comments/:id/report        - 举报评论"
    echo "GET    /api/v1/comments/user/:userId      - 获取用户评论"
    echo "GET    /api/v1/comments/hot               - 获取热门评论"
    echo "GET    /api/v1/comments/search            - 搜索评论"
    echo "GET    /api/v1/comments/stats             - 获取评论统计"
    echo ""
    echo "🔧 管理员接口:"
    echo "GET    /api/v1/comments/admin/pending     - 获取待审核评论"
    echo "PUT    /api/v1/comments/:id/moderate      - 审核评论"
    echo "PUT    /api/v1/comments/admin/batch-moderate - 批量审核"
    echo ""
    echo "📖 使用示例:"
    echo "curl -X POST http://localhost:3000/api/v1/comments \\"
    echo "  -H 'Authorization: Bearer YOUR_TOKEN' \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{\"content\":\"很好听的歌！\",\"musicId\":\"MUSIC_ID\"}'"
    echo ""
else
    echo ""
    echo "❌ 评论系统测试失败，请检查错误信息"
    exit 1
fi
