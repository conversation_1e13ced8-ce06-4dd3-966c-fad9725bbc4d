const multer = require('multer');
const path = require('path');
const { upload<PERSON><PERSON><PERSON>, BUCKETS } = require('../config/minio');

// File type validation
const fileFilter = (req, file, cb) => {
  const allowedAudioTypes = (process.env.ALLOWED_AUDIO_FORMATS || 'mp3,flac,wav,aac').split(',');
  const allowedImageTypes = (process.env.ALLOWED_IMAGE_FORMATS || 'jpg,jpeg,png,webp').split(',');
  
  const fileExtension = path.extname(file.originalname).toLowerCase().substring(1);
  
  // Check file type based on field name
  if (file.fieldname === 'music' || file.fieldname === 'audio') {
    if (allowedAudioTypes.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid audio format. Allowed formats: ${allowedAudioTypes.join(', ')}`), false);
    }
  } else if (file.fieldname === 'cover' || file.fieldname === 'avatar' || file.fieldname === 'image') {
    if (allowedImageTypes.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid image format. Allowed formats: ${allowedImageTypes.join(', ')}`), false);
    }
  } else {
    cb(new Error('Unknown file field'), false);
  }
};

// File size limits
const getFileSize = (fieldname) => {
  const maxSize = process.env.MAX_FILE_SIZE || '50MB';
  const sizeInBytes = parseSize(maxSize);
  
  // Different limits for different file types
  switch (fieldname) {
    case 'music':
    case 'audio':
      return sizeInBytes; // Full limit for music files
    case 'cover':
    case 'avatar':
    case 'image':
      return Math.min(sizeInBytes, 10 * 1024 * 1024); // Max 10MB for images
    default:
      return 5 * 1024 * 1024; // Default 5MB
  }
};

// Parse size string to bytes
const parseSize = (sizeStr) => {
  const units = {
    'B': 1,
    'KB': 1024,
    'MB': 1024 * 1024,
    'GB': 1024 * 1024 * 1024
  };
  
  const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
  if (!match) {
    throw new Error('Invalid file size format');
  }
  
  const value = parseFloat(match[1]);
  const unit = match[2].toUpperCase();
  
  return Math.floor(value * units[unit]);
};

// Multer configuration for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  fileFilter: fileFilter,
  limits: {
    fileSize: parseSize(process.env.MAX_FILE_SIZE || '50MB')
  }
});

// Upload to MinIO helper function
const uploadToMinIO = async (file, bucketName, objectName) => {
  try {
    const metadata = {
      'Content-Type': file.mimetype,
      'Original-Name': Buffer.from(file.originalname, 'latin1').toString('utf8'),
      'Upload-Date': new Date().toISOString()
    };
    
    const result = await uploadBuffer(bucketName, objectName, file.buffer, metadata);
    
    return {
      success: true,
      objectName: objectName,
      bucket: bucketName,
      size: file.buffer.length,
      mimetype: file.mimetype,
      originalName: file.originalname,
      etag: result.etag
    };
  } catch (error) {
    console.error('MinIO upload error:', error);
    throw new Error(`Failed to upload file: ${error.message}`);
  }
};

// Generate unique filename
const generateFileName = (originalName, prefix = '') => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const extension = path.extname(originalName);
  const baseName = path.basename(originalName, extension);
  
  // Clean filename (remove special characters)
  const cleanBaseName = baseName.replace(/[^a-zA-Z0-9\-_]/g, '_');
  
  return `${prefix}${timestamp}_${random}_${cleanBaseName}${extension}`;
};

// Middleware for music file upload
const uploadMusic = (req, res, next) => {
  const uploadSingle = upload.single('music');
  
  uploadSingle(req, res, async (err) => {
    if (err) {
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            error: 'File too large',
            message: `File size exceeds the limit of ${process.env.MAX_FILE_SIZE || '50MB'}`
          });
        }
      }
      return res.status(400).json({
        error: 'Upload error',
        message: err.message
      });
    }
    
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please select a music file to upload'
      });
    }
    
    try {
      const fileName = generateFileName(req.file.originalname, 'music_');
      const uploadResult = await uploadToMinIO(req.file, BUCKETS.MUSIC, fileName);
      
      req.uploadResult = uploadResult;
      next();
    } catch (error) {
      return res.status(500).json({
        error: 'Upload failed',
        message: error.message
      });
    }
  });
};

// Middleware for image file upload (covers, avatars)
const uploadImage = (fieldName, bucketType = 'IMAGES') => {
  return (req, res, next) => {
    const uploadSingle = upload.single(fieldName);
    
    uploadSingle(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          if (err.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
              error: 'File too large',
              message: 'Image file size exceeds the limit of 10MB'
            });
          }
        }
        return res.status(400).json({
          error: 'Upload error',
          message: err.message
        });
      }
      
      if (!req.file) {
        return res.status(400).json({
          error: 'No file uploaded',
          message: 'Please select an image file to upload'
        });
      }
      
      try {
        const prefix = bucketType === 'AVATARS' ? 'avatar_' : 'cover_';
        const fileName = generateFileName(req.file.originalname, prefix);
        const bucket = BUCKETS[bucketType];
        
        const uploadResult = await uploadToMinIO(req.file, bucket, fileName);
        
        req.uploadResult = uploadResult;
        next();
      } catch (error) {
        return res.status(500).json({
          error: 'Upload failed',
          message: error.message
        });
      }
    });
  };
};

// Specific middleware instances
const uploadCover = uploadImage('cover', 'IMAGES');
const uploadAvatar = uploadImage('avatar', 'AVATARS');

module.exports = {
  upload,
  uploadMusic,
  uploadCover,
  uploadAvatar,
  uploadToMinIO,
  generateFileName
};
