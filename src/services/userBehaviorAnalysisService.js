const UserPreference = require('../models/UserPreference');
const PlayHistory = require('../models/PlayHistory');
const UserBehavior = require('../models/UserBehavior');
const Music = require('../models/Music');
const SimilarityMatrix = require('../models/SimilarityMatrix');

/**
 * 用户行为分析服务
 * 分析用户播放行为，生成用户偏好和相似度数据
 */
class UserBehaviorAnalysisService {
  
  /**
   * 分析用户播放行为并更新偏好
   */
  async analyzeUserBehavior(userId, timeRange = '30d') {
    const startTime = Date.now();
    
    try {
      // 获取用户播放历史
      const playHistory = await this.getUserPlayHistory(userId, timeRange);
      
      if (playHistory.length === 0) {
        console.log(`No play history found for user ${userId}`);
        return null;
      }

      // 分析播放模式
      const behaviorPatterns = await this.analyzeBehaviorPatterns(playHistory);
      
      // 生成用户偏好
      const preferences = await this.generateUserPreferences(userId, playHistory, behaviorPatterns);
      
      // 更新用户偏好模型
      await this.updateUserPreferenceModel(userId, preferences, behaviorPatterns);
      
      console.log(`User behavior analysis completed for user ${userId} in ${Date.now() - startTime}ms`);
      
      return {
        userId,
        analysisTime: Date.now() - startTime,
        playHistoryCount: playHistory.length,
        preferences,
        behaviorPatterns
      };
      
    } catch (error) {
      console.error(`Error analyzing user behavior for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * 获取用户播放历史
   */
  async getUserPlayHistory(userId, timeRange) {
    const now = new Date();
    let startDate;
    
    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    return PlayHistory.find({
      userId,
      startTime: { $gte: startDate }
    })
    .populate('musicId', 'title artist album genre duration quality')
    .sort({ startTime: -1 });
  }

  /**
   * 分析行为模式
   */
  async analyzeBehaviorPatterns(playHistory) {
    const patterns = {
      totalPlays: playHistory.length,
      completedPlays: 0,
      skippedPlays: 0,
      totalPlayTime: 0,
      avgPlayDuration: 0,
      completionRate: 0,
      skipRate: 0,
      timePatterns: this.analyzeTimePatterns(playHistory),
      sourcePatterns: this.analyzeSourcePatterns(playHistory),
      qualityPatterns: this.analyzeQualityPatterns(playHistory),
      sessionPatterns: this.analyzeSessionPatterns(playHistory)
    };

    // 计算基础统计
    playHistory.forEach(play => {
      if (play.isCompleted) {
        patterns.completedPlays++;
      }
      if (play.isSkipped) {
        patterns.skippedPlays++;
      }
      patterns.totalPlayTime += play.playDuration;
    });

    patterns.avgPlayDuration = patterns.totalPlayTime / patterns.totalPlays;
    patterns.completionRate = (patterns.completedPlays / patterns.totalPlays) * 100;
    patterns.skipRate = (patterns.skippedPlays / patterns.totalPlays) * 100;

    return patterns;
  }

  /**
   * 分析时间模式
   */
  analyzeTimePatterns(playHistory) {
    const hourlyDistribution = new Array(24).fill(0);
    const dailyDistribution = new Array(7).fill(0);
    
    playHistory.forEach(play => {
      const hour = play.startTime.getHours();
      const day = play.startTime.getDay();
      
      hourlyDistribution[hour]++;
      dailyDistribution[day]++;
    });

    return {
      hourlyDistribution,
      dailyDistribution,
      peakHour: hourlyDistribution.indexOf(Math.max(...hourlyDistribution)),
      peakDay: dailyDistribution.indexOf(Math.max(...dailyDistribution))
    };
  }

  /**
   * 分析播放来源模式
   */
  analyzeSourcePatterns(playHistory) {
    const sourceCount = {};
    
    playHistory.forEach(play => {
      const source = play.playSource || 'unknown';
      sourceCount[source] = (sourceCount[source] || 0) + 1;
    });

    return sourceCount;
  }

  /**
   * 分析音质偏好模式
   */
  analyzeQualityPatterns(playHistory) {
    const qualityCount = {};
    
    playHistory.forEach(play => {
      if (play.musicId && play.musicId.quality) {
        const quality = play.musicId.quality;
        qualityCount[quality] = (qualityCount[quality] || 0) + 1;
      }
    });

    return qualityCount;
  }

  /**
   * 分析会话模式
   */
  analyzeSessionPatterns(playHistory) {
    const sessions = new Map();
    
    playHistory.forEach(play => {
      const sessionId = play.sessionId || 'unknown';
      if (!sessions.has(sessionId)) {
        sessions.set(sessionId, {
          playCount: 0,
          totalDuration: 0,
          startTime: play.startTime,
          endTime: play.startTime
        });
      }
      
      const session = sessions.get(sessionId);
      session.playCount++;
      session.totalDuration += play.playDuration;
      
      if (play.startTime < session.startTime) {
        session.startTime = play.startTime;
      }
      if (play.startTime > session.endTime) {
        session.endTime = play.startTime;
      }
    });

    const sessionArray = Array.from(sessions.values());
    const avgSessionLength = sessionArray.reduce((sum, s) => sum + s.playCount, 0) / sessionArray.length;
    const avgSessionDuration = sessionArray.reduce((sum, s) => sum + s.totalDuration, 0) / sessionArray.length;

    return {
      totalSessions: sessionArray.length,
      avgSessionLength,
      avgSessionDuration
    };
  }

  /**
   * 生成用户偏好
   */
  async generateUserPreferences(userId, playHistory, behaviorPatterns) {
    const preferences = {
      genres: new Map(),
      artists: new Map(),
      qualities: new Map(),
      timeSlots: new Map()
    };

    // 分析流派偏好
    playHistory.forEach(play => {
      if (play.musicId && play.musicId.genre) {
        const genre = play.musicId.genre;
        const weight = this.calculatePlayWeight(play);
        
        if (preferences.genres.has(genre)) {
          const existing = preferences.genres.get(genre);
          existing.weight += weight;
          existing.playCount++;
          existing.lastPlayed = Math.max(existing.lastPlayed, play.startTime.getTime());
        } else {
          preferences.genres.set(genre, {
            weight,
            playCount: 1,
            lastPlayed: play.startTime.getTime()
          });
        }
      }
    });

    // 分析艺术家偏好
    playHistory.forEach(play => {
      if (play.musicId && play.musicId.artist) {
        const artist = play.musicId.artist;
        const weight = this.calculatePlayWeight(play);
        
        if (preferences.artists.has(artist)) {
          const existing = preferences.artists.get(artist);
          existing.weight += weight;
          existing.playCount++;
          existing.lastPlayed = Math.max(existing.lastPlayed, play.startTime.getTime());
        } else {
          preferences.artists.set(artist, {
            weight,
            playCount: 1,
            lastPlayed: play.startTime.getTime()
          });
        }
      }
    });

    // 转换为数组并排序
    const genrePreferences = Array.from(preferences.genres.entries())
      .map(([genre, data]) => ({ genre, ...data, lastPlayed: new Date(data.lastPlayed) }))
      .sort((a, b) => b.weight - a.weight)
      .slice(0, 50); // 保留前50个

    const artistPreferences = Array.from(preferences.artists.entries())
      .map(([artist, data]) => ({ artist, ...data, lastPlayed: new Date(data.lastPlayed) }))
      .sort((a, b) => b.weight - a.weight)
      .slice(0, 100); // 保留前100个

    return {
      genrePreferences,
      artistPreferences,
      qualityPreferences: behaviorPatterns.qualityPatterns,
      timePreferences: behaviorPatterns.timePatterns.hourlyDistribution.map((count, hour) => ({
        hour,
        playCount: count,
        weight: (count / behaviorPatterns.totalPlays) * 100
      }))
    };
  }

  /**
   * 计算播放权重
   */
  calculatePlayWeight(play) {
    let weight = 1;
    
    // 基于完成度的权重
    if (play.isCompleted) {
      weight = 5;
    } else if (play.playProgress > 80) {
      weight = 4;
    } else if (play.playProgress > 60) {
      weight = 3;
    } else if (play.playProgress > 40) {
      weight = 2;
    } else if (play.playProgress < 10) {
      weight = 0.1;
    }
    
    // 跳过的音乐降低权重
    if (play.isSkipped) {
      weight *= 0.3;
    }
    
    // 时间衰减 - 越近期的播放权重越高
    const daysSincePlay = (Date.now() - play.startTime.getTime()) / (1000 * 60 * 60 * 24);
    const timeDecay = Math.exp(-daysSincePlay / 30); // 30天半衰期
    weight *= timeDecay;
    
    return weight;
  }

  /**
   * 更新用户偏好模型
   */
  async updateUserPreferenceModel(userId, preferences, behaviorPatterns) {
    let userPreference = await UserPreference.findOne({ userId });
    
    if (!userPreference) {
      userPreference = new UserPreference({ userId });
    }

    // 更新流派偏好
    userPreference.genrePreferences = preferences.genrePreferences;
    
    // 更新艺术家偏好
    userPreference.artistPreferences = preferences.artistPreferences;
    
    // 更新音质偏好
    userPreference.qualityPreferences = preferences.qualityPreferences;
    
    // 更新时间偏好
    userPreference.timePreferences = preferences.timePreferences;
    
    // 更新行为特征
    userPreference.behaviorProfile = {
      avgPlayDuration: behaviorPatterns.avgPlayDuration,
      completionRate: behaviorPatterns.completionRate,
      skipRate: behaviorPatterns.skipRate,
      activityScore: this.calculateActivityScore(behaviorPatterns),
      explorationScore: this.calculateExplorationScore(preferences),
      diversityScore: this.calculateDiversityScore(preferences)
    };
    
    // 更新统计信息
    userPreference.updateStats = {
      totalPlays: behaviorPatterns.totalPlays,
      lastPlayTime: new Date(),
      lastUpdated: new Date(),
      updateCount: (userPreference.updateStats?.updateCount || 0) + 1
    };

    await userPreference.save();
    return userPreference;
  }

  /**
   * 计算活跃度分数
   */
  calculateActivityScore(behaviorPatterns) {
    const { totalPlays, avgSessionLength, avgSessionDuration } = behaviorPatterns;
    
    // 基于播放次数的分数
    const playScore = Math.min(50, totalPlays * 2);
    
    // 基于会话长度的分数
    const sessionScore = Math.min(25, avgSessionLength * 5);
    
    // 基于会话时长的分数
    const durationScore = Math.min(25, avgSessionDuration / 60); // 分钟转换
    
    return Math.min(100, playScore + sessionScore + durationScore);
  }

  /**
   * 计算探索性分数
   */
  calculateExplorationScore(preferences) {
    const genreCount = preferences.genrePreferences.length;
    const artistCount = preferences.artistPreferences.length;
    
    // 基于流派多样性
    const genreScore = Math.min(50, genreCount * 5);
    
    // 基于艺术家多样性
    const artistScore = Math.min(50, artistCount * 2);
    
    return Math.min(100, genreScore + artistScore);
  }

  /**
   * 计算多样性分数
   */
  calculateDiversityScore(preferences) {
    // 计算流派权重分布的均匀度
    const genreWeights = preferences.genrePreferences.map(g => g.weight);
    const artistWeights = preferences.artistPreferences.map(a => a.weight);
    
    const genreDiversity = this.calculateEntropy(genreWeights);
    const artistDiversity = this.calculateEntropy(artistWeights);
    
    return Math.min(100, (genreDiversity + artistDiversity) * 50);
  }

  /**
   * 计算熵值（多样性指标）
   */
  calculateEntropy(weights) {
    if (weights.length === 0) return 0;
    
    const total = weights.reduce((sum, w) => sum + w, 0);
    if (total === 0) return 0;
    
    const probabilities = weights.map(w => w / total);
    const entropy = -probabilities.reduce((sum, p) => {
      return p > 0 ? sum + p * Math.log2(p) : sum;
    }, 0);
    
    const maxEntropy = Math.log2(weights.length);
    return maxEntropy > 0 ? entropy / maxEntropy : 0;
  }
}

module.exports = new UserBehaviorAnalysisService();
