const { parseBuffer } = require('music-metadata');
const path = require('path');
const iconv = require('iconv-lite');
const sharp = require('sharp');

class AudioMetadataService {
  /**
   * 解析LRC格式歌词
   * @param {string} lrcText - LRC格式的歌词文本
   * @returns {Object} 解析后的歌词对象
   */
  static parseLrcLyrics(lrcText) {
    if (!lrcText || typeof lrcText !== 'string') {
      return null;
    }

    try {
      const lines = lrcText.split('\n');
      const lyrics = [];
      const metadata = {};

      // LRC时间标签正则表达式 [mm:ss.xx] 或 [mm:ss]
      const timeRegex = /\[(\d{1,2}):(\d{2})(?:\.(\d{1,3}))?\]/g;
      const metaRegex = /\[(\w+):(.*?)\]/;

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        // 检查是否是元数据行（不包含时间标签的方括号行）
        const metaMatch = trimmedLine.match(metaRegex);
        if (metaMatch && !trimmedLine.match(/\[\d{1,2}:\d{2}/)) {
          const [, key, value] = metaMatch;
          metadata[key.toLowerCase()] = value.trim();
          continue;
        }

        // 解析时间标签和歌词
        const timeMatches = [...trimmedLine.matchAll(timeRegex)];
        if (timeMatches.length > 0) {
          const lyricText = trimmedLine.replace(timeRegex, '').trim();

          for (const match of timeMatches) {
            const minutes = parseInt(match[1]);
            const seconds = parseInt(match[2]);
            const milliseconds = match[3] ? parseInt(match[3].padEnd(3, '0')) : 0;

            const timeInMs = (minutes * 60 + seconds) * 1000 + milliseconds;

            lyrics.push({
              time: timeInMs,
              timeFormatted: `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`,
              text: lyricText
            });
          }
        }
      }

      // 按时间排序
      lyrics.sort((a, b) => a.time - b.time);

      return {
        type: 'lrc',
        metadata: metadata,
        lyrics: lyrics,
        hasTimestamps: lyrics.length > 0,
        totalLines: lyrics.length
      };
    } catch (error) {
      console.error('Error parsing LRC lyrics:', error);
      return {
        type: 'text',
        text: lrcText,
        hasTimestamps: false,
        error: error.message
      };
    }
  }

  /**
   * 处理歌词文本
   * @param {string} lyricsText - 原始歌词文本
   * @returns {Object} 处理后的歌词对象
   */
  static processLyrics(lyricsText) {
    if (!lyricsText || typeof lyricsText !== 'string') {
      return null;
    }

    const cleanText = lyricsText.trim();
    if (!cleanText) return null;

    // 检查是否是LRC格式
    const hasLrcFormat = /\[\d{1,2}:\d{2}(?:\.\d{1,3})?\]/.test(cleanText);

    if (hasLrcFormat) {
      return this.parseLrcLyrics(cleanText);
    } else {
      // 普通文本歌词
      const lines = cleanText.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      return {
        type: 'text',
        text: cleanText,
        lines: lines,
        hasTimestamps: false,
        totalLines: lines.length
      };
    }
  }

  /**
   * 检测和转换中文编码
   * @param {string} text - 需要检测的文本
   * @returns {string} 转换后的UTF-8文本
   */
  static detectAndConvertEncoding(text) {
    if (!text || typeof text !== 'string') {
      return text;
    }

    // 如果已经是有效的UTF-8，直接返回
    if (Buffer.from(text, 'utf8').toString('utf8') === text) {
      return text;
    }

    // 尝试常见的中文编码
    const encodings = ['gbk', 'gb2312', 'big5', 'utf8'];

    for (const encoding of encodings) {
      try {
        // 尝试将文本作为指定编码解码
        const buffer = Buffer.from(text, 'binary');
        const decoded = iconv.decode(buffer, encoding);

        // 检查解码结果是否包含有效的中文字符
        if (/[\u4e00-\u9fa5]/.test(decoded)) {
          return decoded;
        }
      } catch (error) {
        // 继续尝试下一个编码
        continue;
      }
    }

    // 如果所有编码都失败，返回原文本
    return text;
  }

  /**
   * 从音频文件缓冲区提取元数据
   * @param {Buffer} buffer - 音频文件缓冲区
   * @param {string} mimeType - 文件MIME类型
   * @returns {Promise<Object>} 提取的元数据
   */
  static async extractMetadata(buffer, mimeType) {
    try {
      // 使用更详细的解析选项
      const metadata = await parseBuffer(buffer, mimeType, {
        duration: true,
        skipCovers: false,
        skipPostHeaders: false,
        includeChapters: false
      });

      // 提取并处理文本字段的编码
      const processTextField = (field) => {
        if (!field) return null;
        if (Array.isArray(field)) {
          return field.map(item => this.detectAndConvertEncoding(item)).join('; ');
        }
        return this.detectAndConvertEncoding(field);
      };

      return {
        // 基本信息 - 处理中文编码
        title: processTextField(metadata.common.title),
        artist: processTextField(metadata.common.artist),
        album: processTextField(metadata.common.album),
        genre: processTextField(metadata.common.genre?.[0]),
        year: metadata.common.year || null,

        // 音频技术信息
        duration: Math.max(0, metadata.format.duration || 0),
        bitrate: Math.min(9999, Math.max(32, metadata.format.bitrate || 128)),
        sampleRate: Math.max(0, metadata.format.sampleRate || 44100),
        channels: Math.max(1, metadata.format.numberOfChannels || 2),

        // 文件格式信息
        container: metadata.format.container || null,
        codec: metadata.format.codec || null,
        lossless: metadata.format.lossless || false,

        // 封面图片
        picture: metadata.common.picture?.[0] || null,

        // 扩展的ID3标签信息 - 处理中文编码
        albumartist: processTextField(metadata.common.albumartist),
        track: metadata.common.track?.no || null,
        disk: metadata.common.disk?.no || null,
        comment: processTextField(metadata.common.comment?.[0]),
        lyrics: processTextField(metadata.common.lyrics?.[0]),

        // 处理歌词（支持LRC格式）
        processedLyrics: this.processLyrics(processTextField(metadata.common.lyrics?.[0])),

        // 新增的ID3标签字段
        composer: processTextField(metadata.common.composer?.[0]),
        conductor: processTextField(metadata.common.conductor?.[0]),
        lyricist: processTextField(metadata.common.lyricist?.[0]),
        remixer: processTextField(metadata.common.remixer?.[0]),
        producer: processTextField(metadata.common.producer?.[0]),
        label: processTextField(metadata.common.label?.[0]),
        copyright: processTextField(metadata.common.copyright),
        encodedby: processTextField(metadata.common.encodedby),
        originaldate: metadata.common.originaldate || null,
        originalyear: metadata.common.originalyear || null,
        releasetype: processTextField(metadata.common.releasetype?.[0]),
        barcode: metadata.common.barcode || null,
        isrc: metadata.common.isrc?.[0] || null,

        // 评级和分组
        rating: metadata.common.rating?.[0]?.rating || null,
        grouping: processTextField(metadata.common.grouping?.[0]),
        mood: processTextField(metadata.common.mood?.[0]),

        // 技术信息
        bpm: metadata.common.bpm || null,
        key: processTextField(metadata.common.key),

        // 原始元数据（用于调试）
        rawMetadata: {
          format: metadata.format,
          common: metadata.common,
          native: metadata.native
        }
      };
    } catch (error) {
      console.error('Error extracting audio metadata:', error);
      throw new Error(`Failed to extract metadata: ${error.message}`);
    }
  }

  /**
   * 根据文件扩展名确定音质等级
   * @param {string} fileFormat - 文件格式
   * @param {number} bitrate - 比特率
   * @param {boolean} lossless - 是否无损
   * @returns {string} 音质等级
   */
  static determineQuality(fileFormat, bitrate, lossless) {
    // 无损格式
    if (lossless || ['flac', 'wav', 'aiff'].includes(fileFormat.toLowerCase())) {
      return 'lossless';
    }
    
    // 根据比特率判断
    if (bitrate >= 320) {
      return 'super';
    } else if (bitrate >= 192) {
      return 'high';
    } else {
      return 'standard';
    }
  }

  /**
   * 验证音频文件格式
   * @param {string} originalName - 原始文件名
   * @param {string} mimeType - MIME类型
   * @returns {Object} 验证结果
   */
  static validateAudioFile(originalName, mimeType) {
    const allowedFormats = (process.env.ALLOWED_AUDIO_FORMATS || 'mp3,flac,wav,aac').split(',');
    const fileExtension = path.extname(originalName).toLowerCase().substring(1);
    
    // 检查文件扩展名
    if (!allowedFormats.includes(fileExtension)) {
      return {
        valid: false,
        error: `Unsupported file format: ${fileExtension}. Allowed formats: ${allowedFormats.join(', ')}`
      };
    }
    
    // 检查MIME类型
    const validMimeTypes = {
      'mp3': ['audio/mpeg', 'audio/mp3'],
      'flac': ['audio/flac', 'audio/x-flac'],
      'wav': ['audio/wav', 'audio/wave', 'audio/x-wav'],
      'aac': ['audio/aac', 'audio/x-aac'],
      'm4a': ['audio/mp4', 'audio/x-m4a'],
      'ogg': ['audio/ogg', 'audio/vorbis']
    };
    
    const expectedMimeTypes = validMimeTypes[fileExtension] || [];
    if (expectedMimeTypes.length > 0 && !expectedMimeTypes.includes(mimeType)) {
      console.warn(`MIME type mismatch: expected ${expectedMimeTypes.join(' or ')}, got ${mimeType}`);
      // 不阻止上传，只是警告
    }
    
    return {
      valid: true,
      format: fileExtension
    };
  }

  /**
   * 处理封面图片
   * @param {Object} picture - 图片元数据
   * @param {Object} options - 处理选项
   * @returns {Promise<Object>} 处理后的图片信息
   */
  static async processCoverImage(picture, options = {}) {
    if (!picture || !picture.data) {
      return null;
    }

    try {
      const {
        maxWidth = 800,
        maxHeight = 800,
        quality = 85,
        format = 'jpeg',
        optimize = true
      } = options;

      let imageBuffer = picture.data;
      let processedFormat = picture.format || 'image/jpeg';
      let originalSize = picture.data.length;

      // 获取原始图片信息
      const imageInfo = await sharp(imageBuffer).metadata();

      console.log('Original cover image info:', {
        format: imageInfo.format,
        width: imageInfo.width,
        height: imageInfo.height,
        size: originalSize
      });

      // 检查是否需要处理
      const needsResize = imageInfo.width > maxWidth || imageInfo.height > maxHeight;
      const needsOptimize = optimize && originalSize > 100 * 1024; // 大于100KB才优化

      if (needsResize || needsOptimize || imageInfo.format !== format) {
        let sharpInstance = sharp(imageBuffer);

        // 调整尺寸（保持宽高比）
        if (needsResize) {
          sharpInstance = sharpInstance.resize(maxWidth, maxHeight, {
            fit: 'inside',
            withoutEnlargement: true
          });
        }

        // 转换格式和优化
        if (format === 'jpeg') {
          sharpInstance = sharpInstance.jpeg({
            quality: quality,
            progressive: true,
            mozjpeg: true
          });
          processedFormat = 'image/jpeg';
        } else if (format === 'webp') {
          sharpInstance = sharpInstance.webp({
            quality: quality,
            effort: 6
          });
          processedFormat = 'image/webp';
        } else if (format === 'png') {
          sharpInstance = sharpInstance.png({
            compressionLevel: 9,
            progressive: true
          });
          processedFormat = 'image/png';
        }

        // 处理图片
        imageBuffer = await sharpInstance.toBuffer();

        console.log('Processed cover image:', {
          originalSize: originalSize,
          processedSize: imageBuffer.length,
          compression: ((originalSize - imageBuffer.length) / originalSize * 100).toFixed(1) + '%'
        });
      }

      return {
        format: processedFormat,
        data: imageBuffer,
        description: picture.description || 'Cover',
        size: imageBuffer.length,
        originalSize: originalSize,
        width: imageInfo.width,
        height: imageInfo.height,
        processed: needsResize || needsOptimize || imageInfo.format !== format
      };
    } catch (error) {
      console.error('Error processing cover image:', error);

      // 如果处理失败，返回原始图片
      try {
        return {
          format: picture.format || 'image/jpeg',
          data: picture.data,
          description: picture.description || 'Cover',
          size: picture.data.length,
          originalSize: picture.data.length,
          width: null,
          height: null,
          processed: false,
          error: error.message
        };
      } catch (fallbackError) {
        console.error('Failed to return original image:', fallbackError);
        return null;
      }
    }
  }

  /**
   * 验证和标准化元数据字段
   * @param {Object} metadata - 原始元数据
   * @returns {Object} 验证结果和标准化数据
   */
  static validateAndStandardizeMetadata(metadata) {
    const errors = [];
    const warnings = [];
    const standardized = {};

    // 验证必需字段
    if (!metadata.title || metadata.title.trim().length === 0) {
      errors.push('Title is required');
    }
    if (!metadata.artist || metadata.artist.trim().length === 0) {
      errors.push('Artist is required');
    }

    // 验证音频技术参数
    if (metadata.duration <= 0) {
      warnings.push('Invalid duration detected');
    }
    if (metadata.bitrate < 32 || metadata.bitrate > 9999) {
      warnings.push('Unusual bitrate detected');
    }
    if (metadata.sampleRate < 8000 || metadata.sampleRate > 192000) {
      warnings.push('Unusual sample rate detected');
    }

    // 验证年份
    if (metadata.year) {
      const currentYear = new Date().getFullYear();
      if (metadata.year < 1900 || metadata.year > currentYear + 1) {
        warnings.push('Invalid year detected');
      }
    }

    // 验证评级
    if (metadata.rating && (metadata.rating < 0 || metadata.rating > 5)) {
      warnings.push('Invalid rating detected');
    }

    // 验证BPM
    if (metadata.bpm && (metadata.bpm < 1 || metadata.bpm > 300)) {
      warnings.push('Unusual BPM detected');
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings,
      standardized: standardized
    };
  }

  /**
   * 清理和标准化元数据
   * @param {Object} metadata - 原始元数据
   * @param {string} originalName - 原始文件名
   * @returns {Object} 清理后的元数据
   */
  static cleanMetadata(metadata, originalName) {
    // 如果没有标题，使用文件名（去掉扩展名）
    const title = metadata.title ||
                  path.basename(originalName, path.extname(originalName));

    // 如果没有艺术家，使用默认值
    const artist = metadata.artist || 'Unknown Artist';

    // 清理字符串字段 - 支持中文字符和标准化
    const cleanString = (str, maxLength = 200) => {
      if (!str) return null;
      let cleaned = str.toString().trim();

      // 移除多余的空白字符
      cleaned = cleaned.replace(/\s+/g, ' ');

      // 移除特殊控制字符
      cleaned = cleaned.replace(/[\x00-\x1F\x7F]/g, '');

      // 标准化引号
      cleaned = cleaned.replace(/[""]/g, '"').replace(/['']/g, "'");

      // 使用字符长度而不是字节长度来限制中文字符
      if (cleaned.length > maxLength) {
        return cleaned.substring(0, maxLength);
      }
      return cleaned;
    };

    // 标准化艺术家名称
    const standardizeArtist = (artist) => {
      if (!artist) return null;
      let cleaned = cleanString(artist);

      // 处理多个艺术家的分隔符
      cleaned = cleaned.replace(/[;；,，&＆]/g, '; ');
      cleaned = cleaned.replace(/\s*;\s*/g, '; ');

      return cleaned;
    };

    // 标准化流派
    const standardizeGenre = (genre) => {
      if (!genre) return null;
      let cleaned = cleanString(genre);

      // 常见流派标准化
      const genreMap = {
        'pop': 'Pop',
        'rock': 'Rock',
        'jazz': 'Jazz',
        'classical': 'Classical',
        'electronic': 'Electronic',
        'hip hop': 'Hip Hop',
        'r&b': 'R&B',
        'country': 'Country',
        'folk': 'Folk',
        'blues': 'Blues'
      };

      const lowerGenre = cleaned.toLowerCase();
      return genreMap[lowerGenre] || cleaned;
    };

    // 验证年份
    const validateYear = (year) => {
      if (!year) return null;
      const numYear = parseInt(year);
      return numYear > 1900 && numYear <= new Date().getFullYear() + 1 ? numYear : null;
    };

    // 验证数字字段
    const validateNumber = (num, min = 0, max = null) => {
      if (!num) return null;
      const parsed = parseInt(num);
      if (isNaN(parsed) || parsed < min) return null;
      if (max && parsed > max) return null;
      return parsed;
    };

    // 执行验证
    const validation = this.validateAndStandardizeMetadata(metadata);

    const cleanedData = {
      // 基本信息
      title: cleanString(title),
      artist: standardizeArtist(artist),
      album: cleanString(metadata.album),
      genre: standardizeGenre(metadata.genre),
      year: validateYear(metadata.year),

      // 音频技术信息
      duration: Math.max(0, metadata.duration || 0),
      bitrate: Math.max(0, metadata.bitrate || 0),
      sampleRate: Math.max(0, metadata.sampleRate || 0),
      channels: Math.max(1, metadata.channels || 2),

      // 文件格式信息
      container: metadata.container,
      codec: metadata.codec,
      lossless: Boolean(metadata.lossless),
      picture: metadata.picture,

      // 扩展的ID3标签信息
      albumartist: cleanString(metadata.albumartist),
      track: validateNumber(metadata.track, 1, 999),
      disk: validateNumber(metadata.disk, 1, 99),
      comment: cleanString(metadata.comment, 500),
      lyrics: metadata.lyrics ? cleanString(metadata.lyrics, 10000) : null,
      processedLyrics: metadata.processedLyrics,

      // 新增的ID3标签字段
      composer: cleanString(metadata.composer),
      conductor: cleanString(metadata.conductor),
      lyricist: cleanString(metadata.lyricist),
      remixer: cleanString(metadata.remixer),
      producer: cleanString(metadata.producer),
      label: cleanString(metadata.label),
      copyright: cleanString(metadata.copyright, 300),
      encodedby: cleanString(metadata.encodedby),
      originaldate: metadata.originaldate,
      originalyear: validateYear(metadata.originalyear),
      releasetype: cleanString(metadata.releasetype),
      barcode: cleanString(metadata.barcode, 50),
      isrc: cleanString(metadata.isrc, 20),

      // 评级和分组
      rating: validateNumber(metadata.rating, 0, 5),
      grouping: cleanString(metadata.grouping),
      mood: cleanString(metadata.mood),

      // 技术信息
      bpm: validateNumber(metadata.bpm, 1, 300),
      key: cleanString(metadata.key, 10),

      // 保留原始元数据用于调试
      rawMetadata: metadata.rawMetadata,

      // 验证结果
      validation: validation
    };

    return cleanedData;
  }

  /**
   * 生成音乐文件的完整信息
   * @param {Buffer} buffer - 文件缓冲区
   * @param {Object} fileInfo - 文件基本信息
   * @returns {Promise<Object>} 完整的音乐信息
   */
  static async generateMusicInfo(buffer, fileInfo) {
    try {
      const { originalName, fileName, mimeType, size } = fileInfo;
      
      // 验证文件格式
      const validation = this.validateAudioFile(originalName, mimeType);
      if (!validation.valid) {
        throw new Error(validation.error);
      }
      
      // 提取元数据
      const rawMetadata = await this.extractMetadata(buffer, mimeType);
      
      // 清理元数据
      const metadata = this.cleanMetadata(rawMetadata, originalName);
      
      // 确定音质等级
      const quality = this.determineQuality(validation.format, metadata.bitrate, metadata.lossless);
      
      // 处理封面图片
      const coverImage = await this.processCoverImage(metadata.picture, {
        maxWidth: 800,
        maxHeight: 800,
        quality: 85,
        format: 'jpeg',
        optimize: true
      });
      
      return {
        // 基本信息
        title: metadata.title,
        artist: metadata.artist,
        album: metadata.album,
        genre: metadata.genre,
        year: metadata.year,

        // 音频技术信息
        duration: metadata.duration,
        bitrate: metadata.bitrate,
        sampleRate: metadata.sampleRate,
        channels: metadata.channels,

        // 文件信息
        fileSize: size,
        fileName: fileName,
        originalName: originalName,
        fileFormat: validation.format,
        mimeType: mimeType,
        quality: quality,

        // 歌词信息
        lyrics: metadata.lyrics,
        hasLyrics: Boolean(metadata.lyrics),
        processedLyrics: metadata.processedLyrics,
        hasTimestampedLyrics: metadata.processedLyrics?.hasTimestamps || false,

        // 封面图片信息
        coverImage: coverImage,

        // 技术细节
        container: metadata.container,
        codec: metadata.codec,
        lossless: metadata.lossless,

        // 扩展的ID3标签信息
        albumartist: metadata.albumartist,
        track: metadata.track,
        disk: metadata.disk,
        comment: metadata.comment,

        // 创作人员信息
        composer: metadata.composer,
        conductor: metadata.conductor,
        lyricist: metadata.lyricist,
        remixer: metadata.remixer,
        producer: metadata.producer,

        // 发行信息
        label: metadata.label,
        copyright: metadata.copyright,
        encodedby: metadata.encodedby,
        originaldate: metadata.originaldate,
        originalyear: metadata.originalyear,
        releasetype: metadata.releasetype,
        barcode: metadata.barcode,
        isrc: metadata.isrc,

        // 分类和评级
        rating: metadata.rating,
        grouping: metadata.grouping,
        mood: metadata.mood,

        // 音乐特征
        bpm: metadata.bpm,
        key: metadata.key,

        // 标签（可以后续添加）
        tags: [],

        // 原始元数据（调试用）
        rawMetadata: metadata.rawMetadata
      };
    } catch (error) {
      console.error('Error generating music info:', error);
      throw new Error(`Failed to process audio file: ${error.message}`);
    }
  }
}

module.exports = AudioMetadataService;
