const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

class AudioQualityService {
  /**
   * 使用FFmpeg分析音频文件质量
   * @param {Buffer} audioBuffer - 音频文件缓冲区
   * @param {string} originalName - 原始文件名
   * @returns {Promise<Object>} 音频质量分析结果
   */
  static async analyzeAudioQuality(audioBuffer, originalName) {
    const tempDir = path.join(__dirname, '../../temp');
    const tempFilePath = path.join(tempDir, `temp_${Date.now()}_${originalName}`);

    try {
      // 确保临时目录存在
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // 将缓冲区写入临时文件
      fs.writeFileSync(tempFilePath, audioBuffer);

      // 使用FFmpeg分析音频文件
      const audioInfo = await this.getAudioInfo(tempFilePath);
      
      // 计算音质等级
      const qualityLevel = this.calculateQualityLevel(audioInfo);
      
      // 检测音频完整性
      const integrityCheck = await this.checkAudioIntegrity(tempFilePath);
      
      return {
        // 基本音频信息
        duration: audioInfo.duration || 0,
        bitrate: audioInfo.bitrate || 0,
        sampleRate: audioInfo.sampleRate || 0,
        channels: audioInfo.channels || 0,
        
        // 格式信息
        format: audioInfo.format || null,
        codec: audioInfo.codec || null,
        
        // 质量评估
        qualityLevel: qualityLevel,
        qualityScore: this.calculateQualityScore(audioInfo),
        
        // 完整性检查
        isValid: integrityCheck.isValid,
        hasErrors: integrityCheck.hasErrors,
        errorMessages: integrityCheck.errors,
        
        // 技术细节
        fileSize: audioBuffer.length,
        compressionRatio: this.calculateCompressionRatio(audioInfo, audioBuffer.length),
        
        // FFmpeg原始输出（用于调试）
        rawInfo: audioInfo
      };

    } catch (error) {
      console.error('Audio quality analysis error:', error);
      throw new Error(`Failed to analyze audio quality: ${error.message}`);
    } finally {
      // 清理临时文件
      try {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      } catch (cleanupError) {
        console.warn('Failed to cleanup temp file:', cleanupError.message);
      }
    }
  }

  /**
   * 使用FFmpeg获取音频文件信息
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 音频信息
   */
  static async getAudioInfo(filePath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(new Error(`FFprobe error: ${err.message}`));
          return;
        }

        try {
          const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
          
          if (!audioStream) {
            reject(new Error('No audio stream found in file'));
            return;
          }

          const info = {
            duration: parseFloat(metadata.format.duration) || 0,
            bitrate: parseInt(metadata.format.bit_rate) || parseInt(audioStream.bit_rate) || 0,
            sampleRate: parseInt(audioStream.sample_rate) || 0,
            channels: parseInt(audioStream.channels) || 0,
            format: metadata.format.format_name || null,
            codec: audioStream.codec_name || null,
            codecLongName: audioStream.codec_long_name || null,
            profile: audioStream.profile || null,
            level: audioStream.level || null,
            
            // 原始元数据
            metadata: metadata
          };

          resolve(info);
        } catch (parseError) {
          reject(new Error(`Failed to parse audio metadata: ${parseError.message}`));
        }
      });
    });
  }

  /**
   * 检查音频文件完整性
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 完整性检查结果
   */
  static async checkAudioIntegrity(filePath) {
    return new Promise((resolve) => {
      const errors = [];
      let hasErrors = false;

      ffmpeg(filePath)
        .audioCodec('copy')
        .format('null')
        .output('-')
        .on('error', (err) => {
          hasErrors = true;
          errors.push(err.message);
          resolve({
            isValid: false,
            hasErrors: true,
            errors: errors
          });
        })
        .on('end', () => {
          resolve({
            isValid: !hasErrors,
            hasErrors: hasErrors,
            errors: errors
          });
        })
        .run();
    });
  }

  /**
   * 计算音质等级
   * @param {Object} audioInfo - 音频信息
   * @returns {string} 音质等级
   */
  static calculateQualityLevel(audioInfo) {
    const { bitrate, sampleRate, codec, format } = audioInfo;

    // 无损格式
    if (format && (format.includes('flac') || format.includes('wav') || format.includes('aiff'))) {
      return 'lossless';
    }

    // 根据比特率判断
    if (bitrate >= 320000) { // 320 kbps
      return 'super';
    } else if (bitrate >= 192000) { // 192 kbps
      return 'high';
    } else if (bitrate >= 128000) { // 128 kbps
      return 'standard';
    } else {
      return 'low';
    }
  }

  /**
   * 计算音质分数 (0-100)
   * @param {Object} audioInfo - 音频信息
   * @returns {number} 音质分数
   */
  static calculateQualityScore(audioInfo) {
    const { bitrate, sampleRate, channels, codec, format } = audioInfo;
    
    let score = 0;

    // 比特率评分 (40分)
    if (bitrate >= 320000) {
      score += 40;
    } else if (bitrate >= 256000) {
      score += 35;
    } else if (bitrate >= 192000) {
      score += 30;
    } else if (bitrate >= 128000) {
      score += 20;
    } else if (bitrate >= 96000) {
      score += 10;
    }

    // 采样率评分 (30分)
    if (sampleRate >= 96000) {
      score += 30;
    } else if (sampleRate >= 48000) {
      score += 25;
    } else if (sampleRate >= 44100) {
      score += 20;
    } else if (sampleRate >= 22050) {
      score += 10;
    }

    // 声道数评分 (10分)
    if (channels >= 6) {
      score += 10; // 5.1或更多
    } else if (channels >= 2) {
      score += 8;  // 立体声
    } else {
      score += 4;  // 单声道
    }

    // 格式/编码评分 (20分)
    if (format && (format.includes('flac') || format.includes('wav'))) {
      score += 20; // 无损
    } else if (codec === 'aac') {
      score += 15; // AAC
    } else if (codec === 'mp3') {
      score += 12; // MP3
    } else if (codec === 'vorbis') {
      score += 14; // OGG Vorbis
    } else {
      score += 8;  // 其他格式
    }

    return Math.min(100, Math.max(0, score));
  }

  /**
   * 计算压缩比
   * @param {Object} audioInfo - 音频信息
   * @param {number} fileSize - 文件大小（字节）
   * @returns {number} 压缩比
   */
  static calculateCompressionRatio(audioInfo, fileSize) {
    const { duration, sampleRate, channels } = audioInfo;
    
    if (!duration || !sampleRate || !channels) {
      return 0;
    }

    // 计算未压缩大小 (16位PCM)
    const uncompressedSize = duration * sampleRate * channels * 2; // 2字节/样本
    
    if (uncompressedSize === 0) {
      return 0;
    }

    return fileSize / uncompressedSize;
  }

  /**
   * 获取音频格式建议
   * @param {Object} qualityAnalysis - 质量分析结果
   * @returns {Object} 格式建议
   */
  static getFormatRecommendations(qualityAnalysis) {
    const { qualityLevel, qualityScore, bitrate, format } = qualityAnalysis;
    
    const recommendations = {
      currentQuality: qualityLevel,
      score: qualityScore,
      suggestions: []
    };

    if (qualityScore < 50) {
      recommendations.suggestions.push({
        type: 'warning',
        message: 'Audio quality is below average. Consider using higher bitrate or lossless format.'
      });
    }

    if (bitrate < 128000) {
      recommendations.suggestions.push({
        type: 'improvement',
        message: 'Bitrate is quite low. Recommend at least 192 kbps for good quality.'
      });
    }

    if (format && format.includes('mp3') && bitrate >= 320000) {
      recommendations.suggestions.push({
        type: 'optimization',
        message: 'Consider using AAC or OGG Vorbis for better compression at high bitrates.'
      });
    }

    if (qualityLevel === 'lossless') {
      recommendations.suggestions.push({
        type: 'info',
        message: 'Excellent! Lossless format provides the best audio quality.'
      });
    }

    return recommendations;
  }

  /**
   * 批量分析音频质量
   * @param {Array} audioFiles - 音频文件数组 [{buffer, name}]
   * @returns {Promise<Array>} 分析结果数组
   */
  static async batchAnalyzeQuality(audioFiles) {
    const results = [];
    
    for (let i = 0; i < audioFiles.length; i++) {
      const { buffer, name } = audioFiles[i];
      
      try {
        const analysis = await this.analyzeAudioQuality(buffer, name);
        results.push({
          index: i,
          name: name,
          success: true,
          analysis: analysis
        });
      } catch (error) {
        results.push({
          index: i,
          name: name,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

module.exports = AudioQualityService;
