const UserPreference = require('../models/UserPreference');
const RecommendationResult = require('../models/RecommendationResult');
const SimilarityMatrix = require('../models/SimilarityMatrix');
const RecommendationLog = require('../models/RecommendationLog');
const PlayHistory = require('../models/PlayHistory');
const UserBehavior = require('../models/UserBehavior');
const Music = require('../models/Music');
const User = require('../models/User');

/**
 * 推荐系统核心服务
 * 实现多种推荐算法和推荐策略
 */
class RecommendationService {
  constructor() {
    this.algorithms = {
      collaborative_filtering: this.collaborativeFiltering.bind(this),
      content_based: this.contentBasedFiltering.bind(this),
      hybrid: this.hybridRecommendation.bind(this),
      popularity: this.popularityBasedRecommendation.bind(this),
      random: this.randomRecommendation.bind(this)
    };
  }

  /**
   * 生成个性化推荐
   */
  async generatePersonalizedRecommendations(userId, options = {}) {
    const startTime = Date.now();
    const sessionId = this.generateSessionId();
    
    try {
      // 记录推荐请求
      await RecommendationLog.logEvent({
        userId,
        sessionId,
        recommendationType: 'personalized',
        eventType: 'request',
        userContext: {
          currentTime: new Date(),
          ...options.context
        }
      });

      // 获取用户偏好
      const userPreference = await this.getUserPreference(userId);
      
      // 检查缓存的推荐结果
      const cachedResult = await this.getCachedRecommendations(userId, 'personalized');
      if (cachedResult && !options.forceRefresh) {
        await this.logRecommendationDisplay(userId, sessionId, cachedResult);
        return cachedResult;
      }

      // 选择推荐算法
      const algorithm = this.selectAlgorithm(userPreference, options);
      
      // 生成推荐
      const recommendations = await this.algorithms[algorithm](userId, userPreference, options);
      
      // 应用多样性和新颖性过滤
      const filteredRecommendations = await this.applyDiversityAndNovelty(
        recommendations, 
        userPreference, 
        options
      );

      // 保存推荐结果
      const result = await this.saveRecommendationResult(
        userId,
        'personalized',
        filteredRecommendations,
        algorithm,
        options,
        Date.now() - startTime
      );

      // 记录推荐生成
      await RecommendationLog.logEvent({
        userId,
        sessionId,
        recommendationType: 'personalized',
        eventType: 'generate',
        recommendationResult: {
          resultId: result._id,
          totalRecommendations: filteredRecommendations.length,
          generationTime: Date.now() - startTime,
          confidence: result.algorithmInfo.confidence
        },
        algorithm,
        performance: {
          responseTime: Date.now() - startTime
        }
      });

      return result;

    } catch (error) {
      // 记录错误
      await RecommendationLog.logEvent({
        userId,
        sessionId,
        recommendationType: 'personalized',
        eventType: 'request',
        error: {
          hasError: true,
          errorType: 'algorithm_error',
          errorMessage: error.message,
          errorStack: error.stack
        }
      });

      throw error;
    }
  }

  /**
   * 协同过滤推荐
   */
  async collaborativeFiltering(userId, userPreference, options = {}) {
    const limit = options.limit || 20;
    
    // 获取相似用户
    const similarUsers = await this.findSimilarUsers(userId, 10);
    
    if (similarUsers.length === 0) {
      // 如果没有相似用户，回退到流行度推荐
      return this.popularityBasedRecommendation(userId, userPreference, options);
    }

    // 获取相似用户喜欢的音乐
    const candidateMusic = await this.getCandidateMusicFromSimilarUsers(similarUsers, userId);
    
    // 计算推荐分数
    const scoredRecommendations = await this.scoreRecommendations(
      candidateMusic,
      similarUsers,
      'collaborative_filtering'
    );

    return scoredRecommendations.slice(0, limit);
  }

  /**
   * 基于内容的推荐
   */
  async contentBasedFiltering(userId, userPreference, options = {}) {
    const limit = options.limit || 20;
    
    // 获取用户最喜欢的流派和艺术家
    const topGenres = userPreference.genrePreferences
      .sort((a, b) => b.weight - a.weight)
      .slice(0, 5)
      .map(g => g.genre);
    
    const topArtists = userPreference.artistPreferences
      .sort((a, b) => b.weight - a.weight)
      .slice(0, 10)
      .map(a => a.artist);

    // 获取用户已播放的音乐ID
    const playedMusicIds = await this.getUserPlayedMusic(userId);

    // 查找相似的音乐
    const candidateMusic = await Music.find({
      $and: [
        {
          $or: [
            { genre: { $in: topGenres } },
            { artist: { $in: topArtists } }
          ]
        },
        { _id: { $nin: playedMusicIds } },
        { status: 'approved' }
      ]
    }).limit(limit * 2); // 获取更多候选，后续过滤

    // 计算内容相似度分数
    const scoredRecommendations = candidateMusic.map(music => {
      let score = 0;
      
      // 流派匹配分数
      const genrePreference = userPreference.genrePreferences.find(g => g.genre === music.genre);
      if (genrePreference) {
        score += genrePreference.weight / 100 * 0.6;
      }
      
      // 艺术家匹配分数
      const artistPreference = userPreference.artistPreferences.find(a => a.artist === music.artist);
      if (artistPreference) {
        score += artistPreference.weight / 100 * 0.4;
      }

      return {
        musicId: music._id,
        score: Math.min(1, score),
        reason: this.generateRecommendationReason(music, genrePreference, artistPreference),
        algorithmUsed: 'content_based'
      };
    });

    return scoredRecommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * 混合推荐算法
   */
  async hybridRecommendation(userId, userPreference, options = {}) {
    const limit = options.limit || 20;
    
    // 获取不同算法的推荐结果
    const [collaborativeRecs, contentRecs, popularityRecs] = await Promise.all([
      this.collaborativeFiltering(userId, userPreference, { limit: Math.ceil(limit * 0.5) }),
      this.contentBasedFiltering(userId, userPreference, { limit: Math.ceil(limit * 0.3) }),
      this.popularityBasedRecommendation(userId, userPreference, { limit: Math.ceil(limit * 0.2) })
    ]);

    // 合并和重新评分
    const allRecommendations = [
      ...collaborativeRecs.map(r => ({ ...r, weight: 0.5 })),
      ...contentRecs.map(r => ({ ...r, weight: 0.3 })),
      ...popularityRecs.map(r => ({ ...r, weight: 0.2 }))
    ];

    // 去重并计算最终分数
    const uniqueRecommendations = new Map();
    
    allRecommendations.forEach(rec => {
      const musicId = rec.musicId.toString();
      if (uniqueRecommendations.has(musicId)) {
        const existing = uniqueRecommendations.get(musicId);
        existing.score = Math.max(existing.score, rec.score * rec.weight);
        existing.reason += `, ${rec.reason}`;
      } else {
        uniqueRecommendations.set(musicId, {
          ...rec,
          score: rec.score * rec.weight,
          algorithmUsed: 'hybrid'
        });
      }
    });

    return Array.from(uniqueRecommendations.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * 基于流行度的推荐
   */
  async popularityBasedRecommendation(userId, userPreference, options = {}) {
    const limit = options.limit || 20;
    
    // 获取用户已播放的音乐
    const playedMusicIds = await this.getUserPlayedMusic(userId);
    
    // 获取热门音乐
    const popularMusic = await Music.find({
      _id: { $nin: playedMusicIds },
      status: 'approved'
    })
    .sort({ playCount: -1, favoriteCount: -1 })
    .limit(limit);

    return popularMusic.map((music, index) => ({
      musicId: music._id,
      score: Math.max(0.1, 1 - (index / limit)), // 递减分数
      reason: `热门音乐 (播放量: ${music.playCount})`,
      algorithmUsed: 'popularity'
    }));
  }

  /**
   * 随机推荐
   */
  async randomRecommendation(userId, userPreference, options = {}) {
    const limit = options.limit || 20;
    
    // 获取用户已播放的音乐
    const playedMusicIds = await this.getUserPlayedMusic(userId);
    
    // 随机获取音乐
    const randomMusic = await Music.aggregate([
      {
        $match: {
          _id: { $nin: playedMusicIds },
          status: 'approved'
        }
      },
      { $sample: { size: limit } }
    ]);

    return randomMusic.map(music => ({
      musicId: music._id,
      score: Math.random() * 0.5 + 0.1, // 0.1-0.6的随机分数
      reason: '随机发现',
      algorithmUsed: 'random'
    }));
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取用户偏好，如果不存在则创建
   */
  async getUserPreference(userId) {
    let preference = await UserPreference.findOne({ userId });
    
    if (!preference) {
      preference = new UserPreference({ userId });
      await preference.save();
    }
    
    return preference;
  }

  /**
   * 选择推荐算法
   */
  selectAlgorithm(userPreference, options) {
    if (options.algorithm) {
      return options.algorithm;
    }

    const totalPlays = userPreference.updateStats.totalPlays || 0;

    // 新用户使用流行度推荐
    if (totalPlays < 10) {
      return 'popularity';
    }

    // 活跃用户使用混合推荐
    if (totalPlays > 100) {
      return 'hybrid';
    }

    // 中等活跃用户使用协同过滤
    return 'collaborative_filtering';
  }

  /**
   * 获取缓存的推荐结果
   */
  async getCachedRecommendations(userId, recommendationType) {
    return RecommendationResult.findOne({
      userId,
      recommendationType,
      status: 'active',
      expiresAt: { $gt: new Date() }
    }).populate('recommendations.musicId');
  }

  /**
   * 查找相似用户
   */
  async findSimilarUsers(userId, limit = 10) {
    const similarities = await SimilarityMatrix.find({
      similarityType: 'user_user',
      sourceId: userId,
      status: 'active',
      expiresAt: { $gt: new Date() }
    })
    .sort({ similarity: -1 })
    .limit(limit);

    return similarities.map(s => ({
      userId: s.targetId,
      similarity: s.similarity
    }));
  }

  /**
   * 从相似用户获取候选音乐
   */
  async getCandidateMusicFromSimilarUsers(similarUsers, excludeUserId) {
    const userIds = similarUsers.map(u => u.userId);

    // 获取相似用户最近喜欢的音乐
    const recentBehaviors = await UserBehavior.find({
      userId: { $in: userIds },
      actionType: { $in: ['play', 'like', 'complete'] },
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // 最近30天
    }).populate('musicId');

    // 排除当前用户已播放的音乐
    const excludedMusicIds = await this.getUserPlayedMusic(excludeUserId);

    const candidateMusic = new Map();

    recentBehaviors.forEach(behavior => {
      if (!behavior.musicId || excludedMusicIds.includes(behavior.musicId._id.toString())) {
        return;
      }

      const musicId = behavior.musicId._id.toString();
      const userSimilarity = similarUsers.find(u => u.userId.toString() === behavior.userId.toString())?.similarity || 0;

      if (candidateMusic.has(musicId)) {
        candidateMusic.get(musicId).totalWeight += behavior.weight * userSimilarity;
        candidateMusic.get(musicId).userCount += 1;
      } else {
        candidateMusic.set(musicId, {
          music: behavior.musicId,
          totalWeight: behavior.weight * userSimilarity,
          userCount: 1
        });
      }
    });

    return Array.from(candidateMusic.values());
  }

  /**
   * 为推荐结果评分
   */
  async scoreRecommendations(candidateMusic, similarUsers, algorithm) {
    return candidateMusic.map(candidate => {
      // 基础分数 = 加权平均
      const baseScore = candidate.totalWeight / candidate.userCount;

      // 流行度加成
      const popularityBonus = Math.min(0.2, candidate.music.playCount / 10000);

      // 最终分数
      const finalScore = Math.min(1, baseScore + popularityBonus);

      return {
        musicId: candidate.music._id,
        score: finalScore,
        reason: `${candidate.userCount}个相似用户喜欢`,
        algorithmUsed: algorithm
      };
    }).sort((a, b) => b.score - a.score);
  }

  /**
   * 获取用户已播放的音乐ID列表
   */
  async getUserPlayedMusic(userId) {
    const playHistory = await PlayHistory.find({ userId }).distinct('musicId');
    return playHistory.map(id => id.toString());
  }

  /**
   * 应用多样性和新颖性过滤
   */
  async applyDiversityAndNovelty(recommendations, userPreference, options) {
    const diversityWeight = options.diversityWeight || 0.3;
    const noveltyWeight = options.noveltyWeight || 0.2;

    if (diversityWeight === 0 && noveltyWeight === 0) {
      return recommendations;
    }

    // 获取推荐音乐的详细信息
    const musicIds = recommendations.map(r => r.musicId);
    const musicDetails = await Music.find({ _id: { $in: musicIds } });

    const musicMap = new Map();
    musicDetails.forEach(music => {
      musicMap.set(music._id.toString(), music);
    });

    // 应用多样性过滤
    const diversifiedRecs = this.applyDiversityFilter(recommendations, musicMap, diversityWeight);

    // 应用新颖性过滤
    const finalRecs = this.applyNoveltyFilter(diversifiedRecs, musicMap, userPreference, noveltyWeight);

    return finalRecs;
  }

  /**
   * 应用多样性过滤
   */
  applyDiversityFilter(recommendations, musicMap, diversityWeight) {
    if (diversityWeight === 0) return recommendations;

    const selectedGenres = new Set();
    const selectedArtists = new Set();
    const diversifiedRecs = [];

    for (const rec of recommendations) {
      const music = musicMap.get(rec.musicId.toString());
      if (!music) continue;

      let diversityScore = 1;

      // 流派多样性
      if (selectedGenres.has(music.genre)) {
        diversityScore *= 0.7;
      } else {
        selectedGenres.add(music.genre);
      }

      // 艺术家多样性
      if (selectedArtists.has(music.artist)) {
        diversityScore *= 0.8;
      } else {
        selectedArtists.add(music.artist);
      }

      // 调整最终分数
      const adjustedScore = rec.score * (1 - diversityWeight) + diversityScore * diversityWeight;

      diversifiedRecs.push({
        ...rec,
        score: adjustedScore
      });
    }

    return diversifiedRecs.sort((a, b) => b.score - a.score);
  }

  /**
   * 应用新颖性过滤
   */
  applyNoveltyFilter(recommendations, musicMap, userPreference, noveltyWeight) {
    if (noveltyWeight === 0) return recommendations;

    return recommendations.map(rec => {
      const music = musicMap.get(rec.musicId.toString());
      if (!music) return rec;

      let noveltyScore = 1;

      // 检查用户是否熟悉这个流派
      const genrePreference = userPreference.genrePreferences.find(g => g.genre === music.genre);
      if (genrePreference && genrePreference.weight > 50) {
        noveltyScore *= 0.6; // 熟悉的流派降低新颖性
      }

      // 检查用户是否熟悉这个艺术家
      const artistPreference = userPreference.artistPreferences.find(a => a.artist === music.artist);
      if (artistPreference && artistPreference.weight > 50) {
        noveltyScore *= 0.7; // 熟悉的艺术家降低新颖性
      }

      // 基于音乐的流行度调整新颖性
      if (music.playCount > 10000) {
        noveltyScore *= 0.8; // 热门音乐降低新颖性
      }

      // 调整最终分数
      const adjustedScore = rec.score * (1 - noveltyWeight) + noveltyScore * noveltyWeight;

      return {
        ...rec,
        score: adjustedScore
      };
    }).sort((a, b) => b.score - a.score);
  }

  /**
   * 保存推荐结果
   */
  async saveRecommendationResult(userId, recommendationType, recommendations, algorithm, options, processingTime) {
    // 为推荐结果添加位置信息
    const recommendationsWithPosition = recommendations.map((rec, index) => ({
      ...rec,
      position: index + 1
    }));

    const result = new RecommendationResult({
      userId,
      recommendationType,
      recommendations: recommendationsWithPosition,
      parameters: {
        limit: options.limit || 20,
        genreFilter: options.genreFilter || [],
        artistFilter: options.artistFilter || [],
        qualityFilter: options.qualityFilter || [],
        timeRange: options.timeRange || 'all',
        diversityWeight: options.diversityWeight || 0.3,
        noveltyWeight: options.noveltyWeight || 0.2,
        popularityWeight: options.popularityWeight || 0.1
      },
      algorithmInfo: {
        primaryAlgorithm: algorithm,
        algorithmVersion: '1.0',
        confidence: this.calculateConfidence(recommendations),
        processingTime,
        dataPoints: recommendations.length
      }
    });

    return result.save();
  }

  /**
   * 计算推荐置信度
   */
  calculateConfidence(recommendations) {
    if (recommendations.length === 0) return 0;

    const avgScore = recommendations.reduce((sum, rec) => sum + rec.score, 0) / recommendations.length;
    const scoreVariance = recommendations.reduce((sum, rec) => sum + Math.pow(rec.score - avgScore, 2), 0) / recommendations.length;

    // 基于平均分数和分数方差计算置信度
    const confidenceFromScore = avgScore;
    const confidenceFromVariance = Math.max(0, 1 - scoreVariance);

    return (confidenceFromScore + confidenceFromVariance) / 2;
  }

  /**
   * 记录推荐展示
   */
  async logRecommendationDisplay(userId, sessionId, result) {
    await RecommendationLog.logEvent({
      userId,
      sessionId,
      recommendationType: result.recommendationType,
      eventType: 'display',
      recommendationResult: {
        resultId: result._id,
        totalRecommendations: result.recommendations.length,
        confidence: result.algorithmInfo.confidence
      }
    });

    // 更新推荐结果的展示次数
    result.performance.impressions += 1;
    result.lastAccessedAt = new Date();
    await result.save();
  }

  /**
   * 生成推荐原因
   */
  generateRecommendationReason(music, genrePreference, artistPreference) {
    const reasons = [];

    if (genrePreference) {
      reasons.push(`喜欢${music.genre}音乐`);
    }

    if (artistPreference) {
      reasons.push(`喜欢${music.artist}的作品`);
    }

    if (reasons.length === 0) {
      reasons.push('为你推荐');
    }

    return reasons.join('，');
  }

  /**
   * 更新用户偏好
   */
  async updateUserPreference(userId, musicId, playData) {
    const { playDuration, isCompleted, isSkipped } = playData;

    // 获取音乐信息
    const music = await Music.findById(musicId);
    if (!music) return;

    // 获取或创建用户偏好
    let preference = await UserPreference.findOne({ userId });
    if (!preference) {
      preference = new UserPreference({ userId });
    }

    // 更新流派偏好
    if (music.genre) {
      preference.updateGenrePreference(music.genre, playDuration, isCompleted);
    }

    // 更新艺术家偏好
    if (music.artist) {
      preference.updateArtistPreference(music.artist, playDuration, isCompleted);
    }

    // 更新音质偏好
    if (music.quality) {
      preference.qualityPreferences[music.quality] += 1;
    }

    // 更新行为特征
    preference.updateBehaviorProfile({
      playDuration,
      isCompleted,
      isSkipped,
      totalDuration: music.duration
    });

    // 更新统计信息
    preference.updateStats.totalPlays += 1;
    preference.updateStats.lastPlayTime = new Date();
    preference.updateStats.lastUpdated = new Date();
    preference.updateStats.updateCount += 1;

    await preference.save();
  }

  /**
   * 记录推荐反馈
   */
  async recordRecommendationFeedback(userId, musicId, feedbackType, feedbackData = {}) {
    const sessionId = this.generateSessionId();

    await RecommendationLog.logEvent({
      userId,
      sessionId,
      recommendationType: 'personalized', // 可以根据实际情况调整
      eventType: feedbackType,
      musicId,
      userFeedback: {
        explicitFeedback: feedbackData.explicitFeedback,
        implicitFeedback: {
          dwellTime: feedbackData.dwellTime || 0,
          playDuration: feedbackData.playDuration || 0,
          isCompleted: feedbackData.isCompleted || false,
          isSkipped: feedbackData.isSkipped || false
        }
      }
    });

    // 如果是播放反馈，更新用户偏好
    if (feedbackType === 'play' && feedbackData.playDuration) {
      await this.updateUserPreference(userId, musicId, feedbackData);
    }
  }

  /**
   * 获取相似音乐推荐
   */
  async getSimilarMusicRecommendations(musicId, userId, limit = 10) {
    // 获取音乐信息
    const music = await Music.findById(musicId);
    if (!music) {
      throw new Error('Music not found');
    }

    // 获取用户已播放的音乐
    const playedMusicIds = await this.getUserPlayedMusic(userId);

    // 基于流派和艺术家查找相似音乐
    const similarMusic = await Music.find({
      $and: [
        {
          $or: [
            { genre: music.genre },
            { artist: music.artist }
          ]
        },
        { _id: { $ne: musicId } },
        { _id: { $nin: playedMusicIds } },
        { status: 'approved' }
      ]
    }).limit(limit * 2);

    // 计算相似度分数
    const scoredRecommendations = similarMusic.map(similarMusic => {
      let score = 0;

      // 流派匹配
      if (similarMusic.genre === music.genre) {
        score += 0.6;
      }

      // 艺术家匹配
      if (similarMusic.artist === music.artist) {
        score += 0.4;
      }

      // 流行度加成
      score += Math.min(0.2, similarMusic.playCount / 10000);

      return {
        musicId: similarMusic._id,
        score: Math.min(1, score),
        reason: this.generateSimilarMusicReason(music, similarMusic),
        algorithmUsed: 'content_based'
      };
    });

    return scoredRecommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * 生成相似音乐推荐原因
   */
  generateSimilarMusicReason(originalMusic, similarMusic) {
    const reasons = [];

    if (originalMusic.genre === similarMusic.genre) {
      reasons.push(`同为${originalMusic.genre}音乐`);
    }

    if (originalMusic.artist === similarMusic.artist) {
      reasons.push(`同一艺术家作品`);
    }

    return reasons.length > 0 ? reasons.join('，') : '相似音乐';
  }
}

module.exports = new RecommendationService();
