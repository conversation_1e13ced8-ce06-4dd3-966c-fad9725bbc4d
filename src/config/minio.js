const Minio = require('minio');

// MinIO client configuration
const minioClient = new Minio.Client({
  endPoint: process.env.MINIO_ENDPOINT || 'localhost',
  port: parseInt(process.env.MINIO_PORT) || 9000,
  useSSL: process.env.MINIO_USE_SSL === 'true',
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin'
});

// Bucket names
const BUCKETS = {
  MUSIC: process.env.MINIO_BUCKET_MUSIC || 'music',
  IMAGES: process.env.MINIO_BUCKET_IMAGES || 'images',
  AVATARS: process.env.MINIO_BUCKET_AVATARS || 'avatars'
};

// Initialize MinIO buckets
const initializeBuckets = async () => {
  try {
    console.log('🗄️  Initializing MinIO buckets...');
    
    for (const [key, bucketName] of Object.entries(BUCKETS)) {
      const exists = await minioClient.bucketExists(bucketName);
      
      if (!exists) {
        await minioClient.makeBucket(bucketName, 'us-east-1');
        console.log(`✅ Created bucket: ${bucketName}`);
        
        // Set bucket policy for public read access (for images and avatars)
        if (bucketName === BUCKETS.IMAGES || bucketName === BUCKETS.AVATARS) {
          const policy = {
            Version: '2012-10-17',
            Statement: [
              {
                Effect: 'Allow',
                Principal: { AWS: ['*'] },
                Action: ['s3:GetObject'],
                Resource: [`arn:aws:s3:::${bucketName}/*`]
              }
            ]
          };
          
          await minioClient.setBucketPolicy(bucketName, JSON.stringify(policy));
          console.log(`🔓 Set public read policy for bucket: ${bucketName}`);
        }
      } else {
        console.log(`✅ Bucket already exists: ${bucketName}`);
      }
    }
    
    console.log('✅ MinIO buckets initialized successfully');
  } catch (error) {
    console.error('❌ MinIO bucket initialization failed:', error);
    throw error;
  }
};

// Generate presigned URL for file access
const getPresignedUrl = async (bucketName, objectName, expiry = 24 * 60 * 60) => {
  try {
    return await minioClient.presignedGetObject(bucketName, objectName, expiry);
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    throw error;
  }
};

// Upload file to MinIO
const uploadFile = async (bucketName, objectName, filePath, metadata = {}) => {
  try {
    const result = await minioClient.fPutObject(bucketName, objectName, filePath, metadata);
    console.log(`✅ File uploaded successfully: ${objectName}`);
    return result;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
};

// Upload buffer to MinIO
const uploadBuffer = async (bucketName, objectName, buffer, metadata = {}) => {
  try {
    const result = await minioClient.putObject(bucketName, objectName, buffer, metadata);
    console.log(`✅ Buffer uploaded successfully: ${objectName}`);
    return result;
  } catch (error) {
    console.error('Error uploading buffer:', error);
    throw error;
  }
};

// Delete file from MinIO
const deleteFile = async (bucketName, objectName) => {
  try {
    await minioClient.removeObject(bucketName, objectName);
    console.log(`✅ File deleted successfully: ${objectName}`);
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
};

// Check if file exists
const fileExists = async (bucketName, objectName) => {
  try {
    await minioClient.statObject(bucketName, objectName);
    return true;
  } catch (error) {
    if (error.code === 'NotFound') {
      return false;
    }
    throw error;
  }
};

module.exports = {
  minioClient,
  BUCKETS,
  initializeBuckets,
  getPresignedUrl,
  uploadFile,
  uploadBuffer,
  deleteFile,
  fileExists
};
