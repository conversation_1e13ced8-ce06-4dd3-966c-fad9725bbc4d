const mongoose = require('mongoose');

const playlistSchema = new mongoose.Schema({
  // 基本信息
  name: {
    type: String,
    required: [true, 'Playlist name is required'],
    trim: true,
    minlength: [1, 'Playlist name cannot be empty'],
    maxlength: [100, 'Playlist name cannot exceed 100 characters']
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters'],
    default: ''
  },
  
  coverImage: {
    type: String,
    default: null // MinIO object name for cover image
  },
  
  // 歌单设置
  isPublic: {
    type: Boolean,
    default: true // 默认公开
  },
  
  isDefault: {
    type: Boolean,
    default: false // 是否为默认歌单（用户注册时自动创建）
  },
  
  // 关联信息
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator is required']
  },
  
  songs: [{
    musicId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Music',
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    },
    order: {
      type: Number,
      default: 0 // 用于歌曲排序
    }
  }],
  
  // 标签和分类
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Tag cannot exceed 30 characters']
  }],
  
  category: {
    type: String,
    enum: ['pop', 'rock', 'jazz', 'classical', 'electronic', 'folk', 'country', 'rap', 'other'],
    default: 'other'
  },
  
  // 统计信息
  playCount: {
    type: Number,
    default: 0,
    min: [0, 'Play count cannot be negative']
  },
  
  favoriteCount: {
    type: Number,
    default: 0,
    min: [0, 'Favorite count cannot be negative']
  },
  
  shareCount: {
    type: Number,
    default: 0,
    min: [0, 'Share count cannot be negative']
  },
  
  // 时间戳
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  },
  
  lastPlayedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true, // 自动管理 createdAt 和 updatedAt
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段
playlistSchema.virtual('songCount').get(function() {
  return this.songs ? this.songs.length : 0;
});

playlistSchema.virtual('duration').get(function() {
  // 这个需要在查询时通过聚合计算，这里只是占位
  return this._duration || 0;
});

playlistSchema.virtual('formattedCreatedAt').get(function() {
  return this.createdAt.toLocaleDateString('zh-CN');
});

// 索引
playlistSchema.index({ createdBy: 1 });
playlistSchema.index({ name: 'text', description: 'text' }); // 文本搜索
playlistSchema.index({ isPublic: 1, createdAt: -1 }); // 公开歌单按时间排序
playlistSchema.index({ playCount: -1 }); // 热门歌单
playlistSchema.index({ favoriteCount: -1 }); // 收藏排行
playlistSchema.index({ category: 1, isPublic: 1 }); // 分类查询
playlistSchema.index({ 'songs.musicId': 1 }); // 歌曲查询
playlistSchema.index({ tags: 1 }); // 标签查询

// 中间件
playlistSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// 实例方法
playlistSchema.methods.addSong = function(musicId, order = null) {
  // 检查歌曲是否已存在
  const existingSong = this.songs.find(song => song.musicId.toString() === musicId.toString());
  if (existingSong) {
    throw new Error('Song already exists in playlist');
  }
  
  // 如果没有指定顺序，添加到末尾
  if (order === null) {
    order = this.songs.length;
  }
  
  this.songs.push({
    musicId,
    order,
    addedAt: new Date()
  });
  
  // 重新排序
  this.songs.sort((a, b) => a.order - b.order);
  
  return this.save();
};

playlistSchema.methods.removeSong = function(musicId) {
  const songIndex = this.songs.findIndex(song => song.musicId.toString() === musicId.toString());
  if (songIndex === -1) {
    throw new Error('Song not found in playlist');
  }
  
  this.songs.splice(songIndex, 1);
  
  // 重新排序
  this.songs.forEach((song, index) => {
    song.order = index;
  });
  
  return this.save();
};

playlistSchema.methods.reorderSongs = function(songOrders) {
  // songOrders: [{ musicId, order }, ...]
  songOrders.forEach(({ musicId, order }) => {
    const song = this.songs.find(s => s.musicId.toString() === musicId.toString());
    if (song) {
      song.order = order;
    }
  });
  
  this.songs.sort((a, b) => a.order - b.order);
  return this.save();
};

playlistSchema.methods.incrementPlayCount = function() {
  this.playCount += 1;
  this.lastPlayedAt = new Date();
  return this.save();
};

playlistSchema.methods.incrementFavoriteCount = function() {
  this.favoriteCount += 1;
  return this.save();
};

playlistSchema.methods.decrementFavoriteCount = function() {
  this.favoriteCount = Math.max(0, this.favoriteCount - 1);
  return this.save();
};

playlistSchema.methods.incrementShareCount = function() {
  this.shareCount += 1;
  return this.save();
};

// 静态方法
playlistSchema.statics.findPublicPlaylists = function(options = {}) {
  const { page = 1, limit = 20, sort = '-createdAt', category, tags } = options;

  const query = { isPublic: true };

  if (category && category !== 'all') {
    query.category = category;
  }

  if (tags && tags.length > 0) {
    query.tags = { $in: tags };
  }

  return this.find(query)
    .populate('createdBy', 'username avatar')
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit);
};

playlistSchema.statics.findUserPlaylists = function(userId, options = {}) {
  const { includePrivate = true, page = 1, limit = 20 } = options;

  const query = { createdBy: userId };

  if (!includePrivate) {
    query.isPublic = true;
  }

  return this.find(query)
    .populate('songs.musicId', 'title artist duration')
    .sort('-createdAt')
    .skip((page - 1) * limit)
    .limit(limit);
};

playlistSchema.statics.searchPlaylists = function(searchTerm, options = {}) {
  const { page = 1, limit = 20, publicOnly = true } = options;

  const query = {
    $text: { $search: searchTerm }
  };

  if (publicOnly) {
    query.isPublic = true;
  }

  return this.find(query, { score: { $meta: 'textScore' } })
    .populate('createdBy', 'username avatar')
    .sort({ score: { $meta: 'textScore' } })
    .skip((page - 1) * limit)
    .limit(limit);
};

playlistSchema.statics.getPopularPlaylists = function(options = {}) {
  const { page = 1, limit = 20, timeRange = 'all' } = options;

  const query = { isPublic: true };

  // 根据时间范围过滤
  if (timeRange !== 'all') {
    const now = new Date();
    let startDate;

    switch (timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
    }

    if (startDate) {
      query.createdAt = { $gte: startDate };
    }
  }

  return this.find(query)
    .populate('createdBy', 'username avatar')
    .sort('-playCount -favoriteCount')
    .skip((page - 1) * limit)
    .limit(limit);
};

playlistSchema.statics.createDefaultPlaylist = function(userId) {
  return this.create({
    name: '我喜欢的音乐',
    description: '默认歌单，收藏您喜欢的音乐',
    createdBy: userId,
    isDefault: true,
    isPublic: false
  });
};

module.exports = mongoose.model('Playlist', playlistSchema);
