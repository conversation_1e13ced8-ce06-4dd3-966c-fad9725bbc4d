const mongoose = require('mongoose');

/**
 * 播放会话模型
 * 管理用户的播放会话，支持多设备同步和会话恢复
 */
const playSessionSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  
  // 会话ID（唯一标识）
  sessionId: {
    type: String,
    required: [true, 'Session ID is required'],
    unique: true,
    index: true
  },
  
  // 设备信息
  deviceInfo: {
    type: String,
    maxlength: 200,
    required: [true, 'Device info is required']
  },
  
  // 设备类型
  deviceType: {
    type: String,
    enum: ['web', 'mobile', 'desktop', 'tablet', 'tv', 'other'],
    default: 'web'
  },
  
  // 用户代理
  userAgent: {
    type: String,
    maxlength: 500
  },
  
  // IP地址
  ipAddress: {
    type: String,
    maxlength: 45
  },
  
  // 当前播放的音乐ID
  currentMusicId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music',
    default: null
  },
  
  // 当前播放的歌单ID
  currentPlaylistId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Playlist',
    default: null
  },
  
  // 播放状态
  playState: {
    type: String,
    enum: ['playing', 'paused', 'stopped'],
    default: 'stopped'
  },
  
  // 当前播放进度（秒）
  currentProgress: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 音量设置
  volume: {
    type: Number,
    default: 80,
    min: 0,
    max: 100
  },
  
  // 播放模式
  playMode: {
    type: String,
    enum: ['sequential', 'shuffle', 'repeat_one', 'repeat_all'],
    default: 'sequential'
  },
  
  // 播放质量
  playQuality: {
    type: String,
    enum: ['standard', 'high', 'super', 'lossless'],
    default: 'standard'
  },
  
  // 是否静音
  isMuted: {
    type: Boolean,
    default: false
  },
  
  // 会话是否活跃
  isActive: {
    type: Boolean,
    default: true
  },
  
  // 最后心跳时间
  lastHeartbeat: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 会话开始时间
  startTime: {
    type: Date,
    default: Date.now
  },
  
  // 会话结束时间
  endTime: {
    type: Date,
    default: null
  },
  
  // 总播放时长（秒）
  totalPlayTime: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 播放的歌曲数量
  songsPlayed: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // 会话元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'play_sessions'
});

// 索引
playSessionSchema.index({ userId: 1, isActive: 1 });
playSessionSchema.index({ sessionId: 1 });
playSessionSchema.index({ lastHeartbeat: -1 });
playSessionSchema.index({ isActive: 1, lastHeartbeat: -1 });

// 虚拟字段
playSessionSchema.virtual('duration').get(function() {
  const endTime = this.endTime || new Date();
  return Math.floor((endTime - this.startTime) / 1000);
});

playSessionSchema.virtual('isExpired').get(function() {
  const now = new Date();
  const expireTime = 30 * 60 * 1000; // 30分钟无心跳则过期
  return (now - this.lastHeartbeat) > expireTime;
});

// 实例方法

/**
 * 更新心跳
 */
playSessionSchema.methods.updateHeartbeat = function() {
  this.lastHeartbeat = new Date();
  return this.save();
};

/**
 * 更新播放状态
 */
playSessionSchema.methods.updatePlayState = function(state, progress = null) {
  this.playState = state;
  if (progress !== null) {
    this.currentProgress = progress;
  }
  this.lastHeartbeat = new Date();
  return this.save();
};

/**
 * 设置当前播放音乐
 */
playSessionSchema.methods.setCurrentMusic = function(musicId, playlistId = null) {
  this.currentMusicId = musicId;
  this.currentPlaylistId = playlistId;
  this.currentProgress = 0;
  this.songsPlayed++;
  this.lastHeartbeat = new Date();
  return this.save();
};

/**
 * 更新播放设置
 */
playSessionSchema.methods.updateSettings = function(settings) {
  const { volume, playMode, playQuality, isMuted } = settings;
  
  if (volume !== undefined) this.volume = volume;
  if (playMode !== undefined) this.playMode = playMode;
  if (playQuality !== undefined) this.playQuality = playQuality;
  if (isMuted !== undefined) this.isMuted = isMuted;
  
  this.lastHeartbeat = new Date();
  return this.save();
};

/**
 * 结束会话
 */
playSessionSchema.methods.endSession = function() {
  this.isActive = false;
  this.endTime = new Date();
  this.playState = 'stopped';
  return this.save();
};

// 静态方法

/**
 * 创建新会话
 */
playSessionSchema.statics.createSession = async function(userId, sessionId, deviceInfo, options = {}) {
  const { deviceType = 'web', userAgent, ipAddress } = options;
  
  // 结束用户的其他活跃会话（如果需要单会话模式）
  await this.updateMany(
    { userId, isActive: true },
    { isActive: false, endTime: new Date() }
  );
  
  const session = new this({
    userId,
    sessionId,
    deviceInfo,
    deviceType,
    userAgent,
    ipAddress,
    isActive: true,
    startTime: new Date(),
    lastHeartbeat: new Date()
  });
  
  return session.save();
};

/**
 * 获取用户活跃会话
 */
playSessionSchema.statics.getActiveSession = function(userId) {
  return this.findOne({
    userId,
    isActive: true,
    lastHeartbeat: { $gte: new Date(Date.now() - 30 * 60 * 1000) } // 30分钟内有心跳
  }).populate('currentMusicId', 'title artist album duration coverImage')
    .populate('currentPlaylistId', 'name coverImage');
};

/**
 * 清理过期会话
 */
playSessionSchema.statics.cleanupExpiredSessions = async function() {
  const expireTime = new Date(Date.now() - 30 * 60 * 1000); // 30分钟
  
  const result = await this.updateMany(
    {
      isActive: true,
      lastHeartbeat: { $lt: expireTime }
    },
    {
      isActive: false,
      endTime: new Date(),
      playState: 'stopped'
    }
  );
  
  return result.modifiedCount;
};

/**
 * 获取用户会话历史
 */
playSessionSchema.statics.getUserSessions = function(userId, options = {}) {
  const { page = 1, limit = 20, startDate, endDate } = options;
  const skip = (page - 1) * limit;
  
  const query = { userId };
  
  if (startDate || endDate) {
    query.startTime = {};
    if (startDate) query.startTime.$gte = new Date(startDate);
    if (endDate) query.startTime.$lte = new Date(endDate);
  }
  
  return this.find(query)
    .sort({ startTime: -1 })
    .skip(skip)
    .limit(limit)
    .select('sessionId deviceInfo deviceType playState totalPlayTime songsPlayed startTime endTime isActive');
};

const PlaySession = mongoose.model('PlaySession', playSessionSchema);

module.exports = PlaySession;
