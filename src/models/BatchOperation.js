const mongoose = require('mongoose');

/**
 * 批量操作记录模型
 * 用于记录管理员的批量操作历史，便于审计和追踪
 */
const batchOperationSchema = new mongoose.Schema({
  // 操作类型
  operationType: {
    type: String,
    enum: ['batch_review', 'batch_delete', 'batch_update', 'batch_move', 'batch_export'],
    required: [true, 'Operation type is required'],
    index: true
  },
  
  // 操作者ID
  operatorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Operator ID is required'],
    index: true
  },
  
  // 目标音乐IDs
  targetMusicIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Music'
  }],
  
  // 操作参数
  operationParams: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // 操作结果
  result: {
    successCount: {
      type: Number,
      default: 0
    },
    failedCount: {
      type: Number,
      default: 0
    },
    totalCount: {
      type: Number,
      default: 0
    },
    details: [{
      musicId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Music'
      },
      success: {
        type: Boolean,
        default: false
      },
      error: {
        type: String
      },
      previousValue: {
        type: mongoose.Schema.Types.Mixed
      },
      newValue: {
        type: mongoose.Schema.Types.Mixed
      }
    }]
  },
  
  // 操作原因/备注
  reason: {
    type: String,
    maxlength: [500, 'Reason cannot exceed 500 characters']
  },
  
  // 操作状态
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  
  // 开始时间
  startedAt: {
    type: Date,
    default: Date.now
  },
  
  // 完成时间
  completedAt: {
    type: Date
  },
  
  // 操作耗时（毫秒）
  duration: {
    type: Number,
    min: 0
  },
  
  // 错误信息
  error: {
    type: String
  },
  
  // 操作环境信息
  environment: {
    ipAddress: {
      type: String,
      maxlength: 45
    },
    userAgent: {
      type: String,
      maxlength: 500
    },
    sessionId: {
      type: String,
      maxlength: 100
    }
  },
  
  // 额外元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'batch_operations'
});

// 索引
batchOperationSchema.index({ operatorId: 1, createdAt: -1 });
batchOperationSchema.index({ operationType: 1, createdAt: -1 });
batchOperationSchema.index({ status: 1, createdAt: -1 });
batchOperationSchema.index({ startedAt: -1 });

// 实例方法

/**
 * 标记操作开始
 */
batchOperationSchema.methods.markAsStarted = function() {
  this.status = 'processing';
  this.startedAt = new Date();
  return this.save();
};

/**
 * 标记操作完成
 */
batchOperationSchema.methods.markAsCompleted = function(result) {
  this.status = 'completed';
  this.completedAt = new Date();
  this.duration = this.completedAt - this.startedAt;
  
  if (result) {
    this.result = {
      ...this.result,
      ...result
    };
  }
  
  return this.save();
};

/**
 * 标记操作失败
 */
batchOperationSchema.methods.markAsFailed = function(error) {
  this.status = 'failed';
  this.completedAt = new Date();
  this.duration = this.completedAt - this.startedAt;
  this.error = error;
  
  return this.save();
};

/**
 * 添加操作详情
 */
batchOperationSchema.methods.addDetail = function(detail) {
  if (!this.result.details) {
    this.result.details = [];
  }
  
  this.result.details.push(detail);
  
  // 更新计数
  if (detail.success) {
    this.result.successCount = (this.result.successCount || 0) + 1;
  } else {
    this.result.failedCount = (this.result.failedCount || 0) + 1;
  }
  
  this.result.totalCount = this.result.successCount + this.result.failedCount;
  
  return this;
};

// 静态方法

/**
 * 创建批量操作记录
 */
batchOperationSchema.statics.createOperation = async function(operationType, operatorId, targetMusicIds, params = {}) {
  const operation = new this({
    operationType,
    operatorId,
    targetMusicIds,
    operationParams: params.operationParams || {},
    reason: params.reason,
    environment: params.environment || {},
    metadata: params.metadata || {}
  });
  
  await operation.save();
  return operation;
};

/**
 * 获取操作历史
 */
batchOperationSchema.statics.getOperationHistory = async function(options = {}) {
  const {
    page = 1,
    limit = 20,
    operationType,
    operatorId,
    status,
    startDate,
    endDate
  } = options;
  
  const skip = (page - 1) * limit;
  let query = {};
  
  if (operationType) query.operationType = operationType;
  if (operatorId) query.operatorId = operatorId;
  if (status) query.status = status;
  
  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) query.createdAt.$lte = new Date(endDate);
  }
  
  const [operations, total] = await Promise.all([
    this.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('operatorId', 'username profile.displayName')
      .populate('targetMusicIds', 'title artist'),
    this.countDocuments(query)
  ]);
  
  return {
    operations,
    pagination: {
      current: page,
      total: Math.ceil(total / limit),
      count: operations.length,
      totalCount: total
    }
  };
};

/**
 * 获取操作统计
 */
batchOperationSchema.statics.getOperationStats = async function(period = '30d') {
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }
  
  const stats = await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          operationType: '$operationType',
          status: '$status'
        },
        count: { $sum: 1 },
        totalSuccessCount: { $sum: '$result.successCount' },
        totalFailedCount: { $sum: '$result.failedCount' },
        avgDuration: { $avg: '$duration' }
      }
    },
    {
      $group: {
        _id: '$_id.operationType',
        statusStats: {
          $push: {
            status: '$_id.status',
            count: '$count',
            totalSuccessCount: '$totalSuccessCount',
            totalFailedCount: '$totalFailedCount',
            avgDuration: '$avgDuration'
          }
        },
        totalOperations: { $sum: '$count' }
      }
    }
  ]);
  
  return stats;
};

const BatchOperation = mongoose.model('BatchOperation', batchOperationSchema);

module.exports = BatchOperation;
