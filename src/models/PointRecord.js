const mongoose = require('mongoose');

const pointRecordSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  
  type: {
    type: String,
    required: true,
    enum: [
      'register',        // 注册奖励
      'daily_signin',    // 每日签到
      'upload_music',    // 上传音乐
      'share_playlist',  // 分享歌单
      'invite_friend',   // 邀请好友
      'vip_purchase',    // 购买VIP (消费)
      'admin_adjust',    // 管理员调整
      'system_reward',   // 系统奖励
      'penalty'          // 违规扣分
    ]
  },
  
  points: {
    type: Number,
    required: true
  },
  
  balance: {
    type: Number,
    required: true // 操作后的积分余额
  },
  
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Description cannot exceed 200 characters']
  },
  
  // 关联的资源ID (如音乐ID、歌单ID等)
  relatedId: {
    type: mongoose.Schema.Types.ObjectId,
    default: null
  },
  
  // 关联的资源类型
  relatedType: {
    type: String,
    enum: ['music', 'playlist', 'user', null],
    default: null
  },
  
  // 操作的管理员ID (如果是管理员操作)
  operatorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  // 额外的元数据
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// 复合索引
pointRecordSchema.index({ userId: 1, createdAt: -1 });
pointRecordSchema.index({ type: 1, createdAt: -1 });
pointRecordSchema.index({ userId: 1, type: 1 });

// 静态方法：获取用户积分统计
pointRecordSchema.statics.getUserPointsStats = function(userId) {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$type',
        totalPoints: { $sum: '$points' },
        count: { $sum: 1 },
        lastRecord: { $max: '$createdAt' }
      }
    },
    { $sort: { totalPoints: -1 } }
  ]);
};

// 静态方法：获取积分排行榜
pointRecordSchema.statics.getPointsLeaderboard = function(limit = 10) {
  return this.aggregate([
    {
      $group: {
        _id: '$userId',
        totalPoints: { $sum: '$points' },
        recordCount: { $sum: 1 },
        lastActivity: { $max: '$createdAt' }
      }
    },
    { $sort: { totalPoints: -1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    { $unwind: '$user' },
    {
      $project: {
        userId: '$_id',
        username: '$user.username',
        displayName: '$user.profile.displayName',
        avatar: '$user.avatar',
        totalPoints: 1,
        recordCount: 1,
        lastActivity: 1
      }
    }
  ]);
};

// 静态方法：创建积分记录
pointRecordSchema.statics.createRecord = async function(data) {
  const { userId, type, points, description, relatedId, relatedType, operatorId, metadata } = data;
  
  // 获取用户当前积分
  const User = mongoose.model('User');
  const user = await User.findById(userId);
  if (!user) {
    throw new Error('User not found');
  }
  
  // 计算新的积分余额
  const newBalance = user.points + points;
  
  // 创建积分记录
  const record = new this({
    userId,
    type,
    points,
    balance: newBalance,
    description,
    relatedId,
    relatedType,
    operatorId,
    metadata: metadata || {}
  });
  
  // 保存记录并更新用户积分
  await record.save();
  await User.findByIdAndUpdate(userId, { points: newBalance });
  
  return record;
};

// 静态方法：获取系统积分统计
pointRecordSchema.statics.getSystemStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalRecords: { $sum: 1 },
        totalPointsIssued: { $sum: { $cond: [{ $gt: ['$points', 0] }, '$points', 0] } },
        totalPointsConsumed: { $sum: { $cond: [{ $lt: ['$points', 0] }, { $abs: '$points' }, 0] } },
        recordsByType: {
          $push: {
            type: '$type',
            points: '$points'
          }
        }
      }
    },
    {
      $addFields: {
        netPoints: { $subtract: ['$totalPointsIssued', '$totalPointsConsumed'] }
      }
    }
  ]);
};

module.exports = mongoose.model('PointRecord', pointRecordSchema);
