const mongoose = require('mongoose');

const followSchema = new mongoose.Schema({
  // 关注者（发起关注的用户）
  follower: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Follower is required'],
    index: true
  },
  
  // 被关注者（被关注的用户）
  following: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Following user is required'],
    index: true
  },
  
  // 关注状态
  status: {
    type: String,
    enum: ['active', 'blocked', 'pending'], // active: 正常关注, blocked: 被屏蔽, pending: 待确认（私密账户）
    default: 'active'
  },
  
  // 关注来源
  source: {
    type: String,
    enum: ['search', 'recommendation', 'playlist', 'comment', 'manual', 'import'],
    default: 'manual'
  },
  
  // 关注时间
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  // 最后互动时间（用于推荐算法）
  lastInteractionAt: {
    type: Date,
    default: Date.now
  },
  
  // 互动统计
  interactions: {
    // 点赞对方音乐的次数
    likesGiven: {
      type: Number,
      default: 0
    },
    // 评论对方音乐的次数
    commentsGiven: {
      type: Number,
      default: 0
    },
    // 收藏对方歌单的次数
    playlistsShared: {
      type: Number,
      default: 0
    }
  },
  
  // 关注权重（用于推荐算法，基于互动频率）
  weight: {
    type: Number,
    default: 1.0,
    min: 0,
    max: 10
  },
  
  // 是否为相互关注
  isMutual: {
    type: Boolean,
    default: false,
    index: true
  },
  
  // 通知设置
  notifications: {
    // 是否接收被关注者的动态通知
    activities: {
      type: Boolean,
      default: true
    },
    // 是否接收被关注者的新音乐通知
    newMusic: {
      type: Boolean,
      default: true
    },
    // 是否接收被关注者的新歌单通知
    newPlaylists: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 复合索引 - 确保一个用户不能重复关注同一个用户
followSchema.index({ follower: 1, following: 1 }, { unique: true });

// 复合索引 - 优化查询性能
followSchema.index({ follower: 1, status: 1, createdAt: -1 });
followSchema.index({ following: 1, status: 1, createdAt: -1 });
followSchema.index({ isMutual: 1, status: 1 });

// 虚拟字段 - 关注持续时间
followSchema.virtual('followDuration').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// 虚拟字段 - 互动评分
followSchema.virtual('interactionScore').get(function() {
  const { likesGiven, commentsGiven, playlistsShared } = this.interactions;
  return (likesGiven * 1) + (commentsGiven * 2) + (playlistsShared * 3);
});

// 实例方法 - 更新互动统计
followSchema.methods.updateInteraction = function(type, increment = 1) {
  if (this.interactions[type] !== undefined) {
    this.interactions[type] += increment;
    this.lastInteractionAt = new Date();
    
    // 更新权重（基于互动频率）
    const totalInteractions = this.interactionScore;
    this.weight = Math.min(10, 1 + (totalInteractions * 0.1));
    
    return this.save();
  }
  throw new Error(`Invalid interaction type: ${type}`);
};

// 实例方法 - 检查是否为相互关注
followSchema.methods.checkMutualFollow = async function() {
  const Follow = this.constructor;
  const mutualFollow = await Follow.findOne({
    follower: this.following,
    following: this.follower,
    status: 'active'
  });
  
  this.isMutual = !!mutualFollow;
  
  // 如果找到相互关注，也更新对方的isMutual状态
  if (mutualFollow && !mutualFollow.isMutual) {
    mutualFollow.isMutual = true;
    await mutualFollow.save();
  }
  
  return this.save();
};

// 静态方法 - 获取用户的关注列表
followSchema.statics.getFollowing = function(userId, options = {}) {
  const {
    status = 'active',
    limit = 20,
    skip = 0,
    sort = { createdAt: -1 },
    populate = true
  } = options;
  
  let query = this.find({ follower: userId, status });
  
  if (populate) {
    query = query.populate('following', 'username avatar profile.displayName profile.bio');
  }
  
  return query
    .sort(sort)
    .limit(limit)
    .skip(skip)
    .lean();
};

// 静态方法 - 获取用户的粉丝列表
followSchema.statics.getFollowers = function(userId, options = {}) {
  const {
    status = 'active',
    limit = 20,
    skip = 0,
    sort = { createdAt: -1 },
    populate = true
  } = options;
  
  let query = this.find({ following: userId, status });
  
  if (populate) {
    query = query.populate('follower', 'username avatar profile.displayName profile.bio');
  }
  
  return query
    .sort(sort)
    .limit(limit)
    .skip(skip)
    .lean();
};

// 静态方法 - 获取相互关注列表
followSchema.statics.getMutualFollows = function(userId, options = {}) {
  const {
    limit = 20,
    skip = 0,
    sort = { createdAt: -1 }
  } = options;
  
  return this.find({
    follower: userId,
    status: 'active',
    isMutual: true
  })
    .populate('following', 'username avatar profile.displayName profile.bio')
    .sort(sort)
    .limit(limit)
    .skip(skip)
    .lean();
};

// 静态方法 - 检查关注关系
followSchema.statics.checkFollowStatus = function(followerId, followingId) {
  return this.findOne({
    follower: followerId,
    following: followingId
  }).lean();
};

// 静态方法 - 获取用户统计信息
followSchema.statics.getUserStats = async function(userId) {
  const [followingCount, followersCount, mutualCount] = await Promise.all([
    this.countDocuments({ follower: userId, status: 'active' }),
    this.countDocuments({ following: userId, status: 'active' }),
    this.countDocuments({ follower: userId, status: 'active', isMutual: true })
  ]);
  
  return {
    following: followingCount,
    followers: followersCount,
    mutual: mutualCount,
    ratio: followersCount > 0 ? (followingCount / followersCount).toFixed(2) : 0
  };
};

// 静态方法 - 获取推荐关注用户
followSchema.statics.getRecommendedUsers = async function(userId, limit = 10) {
  // 获取用户已关注的用户ID列表
  const following = await this.find({ follower: userId, status: 'active' })
    .select('following')
    .lean();

  const followingIds = following.map(f => f.following);
  followingIds.push(new mongoose.Types.ObjectId(userId)); // 排除自己

  // 基于相互关注的朋友的关注来推荐
  const recommendations = await this.aggregate([
    // 找到用户的相互关注
    {
      $match: {
        follower: new mongoose.Types.ObjectId(userId),
        status: 'active',
        isMutual: true
      }
    },
    // 查找这些相互关注用户的关注
    {
      $lookup: {
        from: 'follows',
        localField: 'following',
        foreignField: 'follower',
        as: 'theirFollowing'
      }
    },
    { $unwind: '$theirFollowing' },
    // 排除已关注的用户
    {
      $match: {
        'theirFollowing.following': { $nin: followingIds },
        'theirFollowing.status': 'active'
      }
    },
    // 按推荐权重分组和排序
    {
      $group: {
        _id: '$theirFollowing.following',
        recommendationScore: { $sum: '$theirFollowing.weight' },
        recommendedBy: { $push: '$following' }
      }
    },
    { $sort: { recommendationScore: -1 } },
    { $limit: limit },
    // 关联用户信息
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    { $unwind: '$user' },
    {
      $project: {
        _id: 1,
        recommendationScore: 1,
        recommendedBy: 1,
        username: '$user.username',
        avatar: '$user.avatar',
        displayName: '$user.profile.displayName',
        bio: '$user.profile.bio'
      }
    }
  ]);

  return recommendations;
};

// 中间件 - 保存前验证
followSchema.pre('save', function(next) {
  // 防止自己关注自己
  if (this.follower.toString() === this.following.toString()) {
    return next(new Error('Users cannot follow themselves'));
  }
  next();
});

// 中间件 - 删除后清理相互关注状态
followSchema.post('findOneAndDelete', async function(doc) {
  if (doc) {
    // 更新对方的相互关注状态
    const mutualFollow = await this.model.findOne({
      follower: doc.following,
      following: doc.follower,
      status: 'active'
    });

    if (mutualFollow && mutualFollow.isMutual) {
      mutualFollow.isMutual = false;
      await mutualFollow.save();
    }
  }
});

// 静态方法 - 获取推荐关注用户
followSchema.statics.getRecommendedUsers = async function(userId, limit = 10) {
  // 获取用户已关注的用户ID列表
  const following = await this.find({ follower: userId, status: 'active' })
    .select('following')
    .lean();

  const followingIds = following.map(f => f.following);
  followingIds.push(new mongoose.Types.ObjectId(userId)); // 排除自己

  // 基于相互关注的朋友的关注来推荐
  const recommendations = await this.aggregate([
    // 找到用户的相互关注
    {
      $match: {
        follower: new mongoose.Types.ObjectId(userId),
        status: 'active',
        isMutual: true
      }
    },
    // 查找这些相互关注用户的关注
    {
      $lookup: {
        from: 'follows',
        localField: 'following',
        foreignField: 'follower',
        as: 'theirFollowing'
      }
    },
    { $unwind: '$theirFollowing' },
    // 排除已关注的用户
    {
      $match: {
        'theirFollowing.following': { $nin: followingIds },
        'theirFollowing.status': 'active'
      }
    },
    // 按推荐权重分组和排序
    {
      $group: {
        _id: '$theirFollowing.following',
        recommendationScore: { $sum: '$theirFollowing.weight' },
        recommendedBy: { $push: '$following' }
      }
    },
    { $sort: { recommendationScore: -1 } },
    { $limit: limit },
    // 关联用户信息
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    { $unwind: '$user' },
    {
      $project: {
        _id: 1,
        recommendationScore: 1,
        recommendedBy: 1,
        username: '$user.username',
        avatar: '$user.avatar',
        displayName: '$user.profile.displayName',
        bio: '$user.profile.bio'
      }
    }
  ]);

  return recommendations;
};

// 中间件 - 保存前验证
followSchema.pre('save', function(next) {
  // 防止自己关注自己
  if (this.follower.toString() === this.following.toString()) {
    return next(new Error('Users cannot follow themselves'));
  }
  next();
});

// 中间件 - 删除后清理相互关注状态
followSchema.post('findOneAndDelete', async function(doc) {
  if (doc) {
    // 更新对方的相互关注状态
    const mutualFollow = await this.model.findOne({
      follower: doc.following,
      following: doc.follower,
      status: 'active'
    });

    if (mutualFollow && mutualFollow.isMutual) {
      mutualFollow.isMutual = false;
      await mutualFollow.save();
    }
  }
});

module.exports = mongoose.model('Follow', followSchema);
