const mongoose = require('mongoose');

const playlistFavoriteSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  
  // 歌单ID
  playlistId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Playlist',
    required: [true, 'Playlist ID is required']
  },
  
  // 收藏时间
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// 复合索引，确保用户不能重复收藏同一个歌单
playlistFavoriteSchema.index({ userId: 1, playlistId: 1 }, { unique: true });

// 其他索引
playlistFavoriteSchema.index({ userId: 1, createdAt: -1 }); // 用户收藏列表按时间排序
playlistFavoriteSchema.index({ playlistId: 1 }); // 歌单收藏统计

// 静态方法
playlistFavoriteSchema.statics.addFavorite = async function(userId, playlistId) {
  try {
    const favorite = new this({ userId, playlistId });
    await favorite.save();
    
    // 增加歌单的收藏数
    const Playlist = mongoose.model('Playlist');
    await Playlist.findByIdAndUpdate(playlistId, { $inc: { favoriteCount: 1 } });
    
    return favorite;
  } catch (error) {
    if (error.code === 11000) {
      throw new Error('Playlist already favorited');
    }
    throw error;
  }
};

playlistFavoriteSchema.statics.removeFavorite = async function(userId, playlistId) {
  const favorite = await this.findOneAndDelete({ userId, playlistId });
  
  if (favorite) {
    // 减少歌单的收藏数
    const Playlist = mongoose.model('Playlist');
    await Playlist.findByIdAndUpdate(playlistId, { $inc: { favoriteCount: -1 } });
  }
  
  return favorite;
};

playlistFavoriteSchema.statics.getUserFavorites = function(userId, options = {}) {
  const { page = 1, limit = 20 } = options;
  
  return this.find({ userId })
    .populate({
      path: 'playlistId',
      populate: {
        path: 'createdBy',
        select: 'username avatar'
      }
    })
    .sort('-createdAt')
    .skip((page - 1) * limit)
    .limit(limit);
};

playlistFavoriteSchema.statics.isFavorited = function(userId, playlistId) {
  return this.exists({ userId, playlistId });
};

playlistFavoriteSchema.statics.getFavoriteCount = function(playlistId) {
  return this.countDocuments({ playlistId });
};

module.exports = mongoose.model('PlaylistFavorite', playlistFavoriteSchema);
