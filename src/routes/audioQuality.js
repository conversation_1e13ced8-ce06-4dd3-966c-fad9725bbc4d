const express = require('express');
const router = express.Router();
const { AudioQualityController, upload } = require('../controllers/audioQualityController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

/**
 * 音频质量检测路由
 * 基础路径: /api/v1/audio-quality
 */

/**
 * @route POST /api/v1/audio-quality/analyze
 * @desc 分析单个音频文件质量
 * @access Private (需要登录)
 * @body multipart/form-data - audio file
 */
router.post('/analyze', 
  authenticateToken,
  upload.single('audio'),
  AudioQualityController.analyzeAudioFile
);

/**
 * @route POST /api/v1/audio-quality/batch-analyze
 * @desc 批量分析音频文件质量
 * @access Private (需要登录)
 * @body multipart/form-data - multiple audio files
 */
router.post('/batch-analyze',
  authenticateToken,
  upload.array('audio', 10), // 最多10个文件
  AudioQualityController.batchAnalyzeAudioFiles
);

/**
 * @route GET /api/v1/audio-quality/report/:musicId
 * @desc 获取已上传音乐的质量报告
 * @access Private (需要登录)
 * @param {string} musicId - 音乐ID
 */
router.get('/report/:musicId',
  authenticateToken,
  AudioQualityController.getMusicQualityReport
);

/**
 * @route GET /api/v1/audio-quality/statistics
 * @desc 获取质量统计信息
 * @access Private (需要登录)
 * @query {string} userId - 可选，指定用户ID获取该用户的统计
 */
router.get('/statistics',
  authenticateToken,
  AudioQualityController.getQualityStatistics
);

/**
 * @route POST /api/v1/audio-quality/reanalyze/:musicId
 * @desc 重新分析已上传音乐的质量
 * @access Private (需要登录，且为上传者或管理员)
 * @param {string} musicId - 音乐ID
 */
router.post('/reanalyze/:musicId',
  authenticateToken,
  AudioQualityController.reanalyzeMusicQuality
);

/**
 * @route GET /api/v1/audio-quality/admin/overview
 * @desc 管理员获取全站质量概览
 * @access Admin only
 */
router.get('/admin/overview',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const Music = require('../models/Music');
      
      // 获取全站质量统计
      const totalMusic = await Music.countDocuments();
      const qualityDistribution = await Music.aggregate([
        {
          $group: {
            _id: '$quality',
            count: { $sum: 1 },
            avgBitrate: { $avg: '$bitrate' },
            avgSize: { $avg: '$fileSize' }
          }
        },
        { $sort: { count: -1 } }
      ]);
      
      // 获取最近上传的音乐质量情况
      const recentUploads = await Music.find({
        qualityAnalysis: { $exists: true }
      })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('title artist quality qualityAnalysis.qualityScore createdAt')
      .populate('uploadedBy', 'username');
      
      // 获取质量问题音乐
      const lowQualityMusic = await Music.find({
        $or: [
          { 'qualityAnalysis.qualityScore': { $lt: 50 } },
          { 'qualityAnalysis.hasErrors': true }
        ]
      })
      .sort({ 'qualityAnalysis.qualityScore': 1 })
      .limit(20)
      .select('title artist quality qualityAnalysis.qualityScore qualityAnalysis.hasErrors')
      .populate('uploadedBy', 'username');
      
      res.json({
        success: true,
        message: 'Admin quality overview retrieved successfully',
        data: {
          overview: {
            totalMusic,
            qualityDistribution,
            lowQualityCount: lowQualityMusic.length
          },
          recentUploads,
          lowQualityMusic,
          timestamp: new Date().toISOString()
        }
      });
      
    } catch (error) {
      console.error('Admin quality overview error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve admin quality overview',
        error: error.message
      });
    }
  }
);

/**
 * @route GET /api/v1/audio-quality/formats
 * @desc 获取支持的音频格式信息
 * @access Public
 */
router.get('/formats', (req, res) => {
  const supportedFormats = {
    lossless: {
      formats: ['FLAC', 'WAV', 'AIFF'],
      description: 'Lossless audio formats with no quality loss',
      recommendedFor: 'Archival, professional use, audiophiles'
    },
    highQuality: {
      formats: ['MP3 320kbps', 'AAC 256kbps+', 'OGG Vorbis 320kbps'],
      description: 'High quality compressed formats',
      recommendedFor: 'General listening, streaming'
    },
    standard: {
      formats: ['MP3 192kbps', 'AAC 128-192kbps'],
      description: 'Standard quality for most use cases',
      recommendedFor: 'Mobile devices, limited bandwidth'
    },
    low: {
      formats: ['MP3 128kbps and below'],
      description: 'Lower quality, smaller file sizes',
      recommendedFor: 'Voice recordings, previews'
    }
  };
  
  const qualityGuidelines = {
    bitrate: {
      excellent: '320+ kbps',
      good: '192-320 kbps',
      acceptable: '128-192 kbps',
      poor: 'Below 128 kbps'
    },
    sampleRate: {
      excellent: '96+ kHz',
      good: '48 kHz',
      standard: '44.1 kHz',
      minimum: '22 kHz'
    },
    channels: {
      surround: '5.1+ channels',
      stereo: '2 channels',
      mono: '1 channel'
    }
  };
  
  res.json({
    success: true,
    message: 'Supported audio formats information',
    data: {
      supportedFormats,
      qualityGuidelines,
      maxFileSize: '100MB',
      recommendedUploadFormat: 'FLAC or high-bitrate MP3/AAC'
    }
  });
});

module.exports = router;
