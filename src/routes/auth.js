const express = require('express');
const { 
  register, 
  login, 
  getProfile, 
  dailySignIn, 
  logout 
} = require('../controllers/authController');
const { 
  authenticateToken, 
  authRateLimit 
} = require('../middleware/auth');

const router = express.Router();

// Public routes (no authentication required)
router.post('/register', authRateLimit, register);
router.post('/login', authRateLimit, login);

// Protected routes (authentication required)
router.get('/profile', authenticateToken, getProfile);
router.post('/signin', authenticateToken, dailySignIn);
router.post('/logout', authenticateToken, logout);

// Test route to verify authentication
router.get('/test', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Authentication successful',
    user: req.user
  });
});

module.exports = router;
