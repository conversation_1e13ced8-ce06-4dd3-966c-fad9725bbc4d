const express = require('express');
const router = express.Router();
const recommendationController = require('../controllers/recommendationController');
const { authenticateToken } = require('../middleware/auth');

/**
 * 推荐系统路由
 * 所有推荐相关的API端点
 */

// 应用认证中间件到所有推荐路由
router.use(authenticateToken);

/**
 * @route   GET /api/v1/recommendations/personalized
 * @desc    获取个性化推荐
 * @access  Private
 * @params  {number} limit - 推荐数量限制 (默认: 20)
 * @params  {string} algorithm - 指定推荐算法 (可选)
 * @params  {boolean} forceRefresh - 强制刷新推荐 (默认: false)
 * @params  {number} diversityWeight - 多样性权重 (默认: 0.3)
 * @params  {number} noveltyWeight - 新颖性权重 (默认: 0.2)
 * @params  {number} popularityWeight - 流行度权重 (默认: 0.1)
 * @params  {string} genreFilter - 流派过滤 (逗号分隔)
 * @params  {string} artistFilter - 艺术家过滤 (逗号分隔)
 * @params  {string} qualityFilter - 音质过滤 (逗号分隔)
 * @params  {string} timeRange - 时间范围 (默认: all)
 */
router.get('/personalized', recommendationController.getPersonalizedRecommendations);

/**
 * @route   GET /api/v1/recommendations/similar/:musicId
 * @desc    获取相似音乐推荐
 * @access  Private
 * @params  {string} musicId - 音乐ID
 * @params  {number} limit - 推荐数量限制 (默认: 10)
 */
router.get('/similar/:musicId', recommendationController.getSimilarMusicRecommendations);

/**
 * @route   GET /api/v1/recommendations/popular
 * @desc    获取热门推荐
 * @access  Private
 * @params  {number} limit - 推荐数量限制 (默认: 20)
 * @params  {string} timeRange - 时间范围 (默认: 7d)
 */
router.get('/popular', recommendationController.getPopularRecommendations);

/**
 * @route   GET /api/v1/recommendations/discover
 * @desc    获取新音乐发现推荐
 * @access  Private
 * @params  {number} limit - 推荐数量限制 (默认: 20)
 */
router.get('/discover', recommendationController.getDiscoveryRecommendations);

/**
 * @route   GET /api/v1/recommendations/preferences
 * @desc    获取用户偏好信息
 * @access  Private
 */
router.get('/preferences', recommendationController.getUserPreferences);

/**
 * @route   GET /api/v1/recommendations/stats
 * @desc    获取推荐统计信息
 * @access  Private
 * @params  {string} timeRange - 时间范围 (默认: 7d)
 */
router.get('/stats', recommendationController.getRecommendationStats);

/**
 * @route   POST /api/v1/recommendations/feedback
 * @desc    记录推荐反馈
 * @access  Private
 * @body    {string} musicId - 音乐ID
 * @body    {string} feedbackType - 反馈类型 (click, play, complete, skip, favorite, share)
 * @body    {number} playDuration - 播放时长 (秒)
 * @body    {boolean} isCompleted - 是否完整播放
 * @body    {boolean} isSkipped - 是否跳过
 * @body    {number} dwellTime - 停留时间 (毫秒)
 * @body    {string} explicitFeedback - 显式反馈 (like, dislike, not_interested, already_known)
 */
router.post('/feedback', recommendationController.recordFeedback);

/**
 * @route   POST /api/v1/recommendations/analyze-behavior
 * @desc    刷新用户行为分析
 * @access  Private
 * @body    {string} timeRange - 分析时间范围 (默认: 30d)
 */
router.post('/analyze-behavior', recommendationController.refreshBehaviorAnalysis);

// 输入验证中间件
const validateRecommendationParams = (req, res, next) => {
  const { limit, diversityWeight, noveltyWeight, popularityWeight } = req.query;
  
  // 验证limit参数
  if (limit && (isNaN(limit) || parseInt(limit) < 1 || parseInt(limit) > 100)) {
    return res.status(400).json({
      success: false,
      message: 'limit参数必须是1-100之间的数字'
    });
  }
  
  // 验证权重参数
  const weights = [diversityWeight, noveltyWeight, popularityWeight];
  for (const weight of weights) {
    if (weight && (isNaN(weight) || parseFloat(weight) < 0 || parseFloat(weight) > 1)) {
      return res.status(400).json({
        success: false,
        message: '权重参数必须是0-1之间的数字'
      });
    }
  }
  
  next();
};

// 应用验证中间件到需要的路由
router.get('/personalized', validateRecommendationParams);
router.get('/popular', validateRecommendationParams);
router.get('/discover', validateRecommendationParams);

// 验证音乐ID参数
const validateMusicId = (req, res, next) => {
  const { musicId } = req.params;
  
  if (!musicId || !musicId.match(/^[0-9a-fA-F]{24}$/)) {
    return res.status(400).json({
      success: false,
      message: '无效的音乐ID格式'
    });
  }
  
  next();
};

router.get('/similar/:musicId', validateMusicId);

// 验证反馈数据
const validateFeedback = (req, res, next) => {
  const { musicId, feedbackType } = req.body;
  
  if (!musicId || !musicId.match(/^[0-9a-fA-F]{24}$/)) {
    return res.status(400).json({
      success: false,
      message: '无效的音乐ID格式'
    });
  }
  
  const validFeedbackTypes = ['click', 'play', 'complete', 'skip', 'favorite', 'share'];
  if (!feedbackType || !validFeedbackTypes.includes(feedbackType)) {
    return res.status(400).json({
      success: false,
      message: '无效的反馈类型'
    });
  }
  
  next();
};

router.post('/feedback', validateFeedback);

// 错误处理中间件
router.use((error, req, res, next) => {
  console.error('Recommendation route error:', error);
  
  res.status(500).json({
    success: false,
    message: '推荐系统内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
