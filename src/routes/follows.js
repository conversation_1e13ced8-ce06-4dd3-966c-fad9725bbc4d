const express = require('express');
const router = express.Router();
const {
  followUser,
  unfollowUser,
  getFollowing,
  getFollowers,
  getMutualFollows,
  checkFollowStatus,
  getUserStats,
  getRecommendedUsers,
  batchFollowUsers
} = require('../controllers/followController');
const { authenticateToken } = require('../middleware/auth');

// 应用认证中间件到所有路由
router.use(authenticateToken);

/**
 * @route   GET /api/v1/follows/mutual
 * @desc    获取当前用户的相互关注列表
 * @access  Private
 * @query   { page?: number, limit?: number, sort?: string }
 */
router.get('/mutual', getMutualFollows);

/**
 * @route   GET /api/v1/follows/recommendations
 * @desc    获取推荐关注的用户
 * @access  Private
 * @query   { limit?: number }
 */
router.get('/recommendations', getRecommendedUsers);

/**
 * @route   POST /api/v1/follows/batch
 * @desc    批量关注用户
 * @access  Private
 * @body    { userIds: string[], source?: string }
 */
router.post('/batch', batchFollowUsers);

/**
 * @route   POST /api/v1/follows/:userId
 * @desc    关注用户
 * @access  Private
 * @body    { source?: string }
 */
router.post('/:userId', followUser);

/**
 * @route   DELETE /api/v1/follows/:userId
 * @desc    取消关注用户
 * @access  Private
 */
router.delete('/:userId', unfollowUser);

/**
 * @route   GET /api/v1/follows/:userId/following
 * @desc    获取用户的关注列表
 * @access  Private
 * @query   { page?: number, limit?: number, sort?: string, status?: string }
 */
router.get('/:userId/following', getFollowing);

/**
 * @route   GET /api/v1/follows/:userId/followers
 * @desc    获取用户的粉丝列表
 * @access  Private
 * @query   { page?: number, limit?: number, sort?: string, status?: string }
 */
router.get('/:userId/followers', getFollowers);

/**
 * @route   GET /api/v1/follows/:userId/status
 * @desc    检查与指定用户的关注状态
 * @access  Private
 */
router.get('/:userId/status', checkFollowStatus);

/**
 * @route   GET /api/v1/follows/:userId/stats
 * @desc    获取用户的关注统计信息
 * @access  Private
 */
router.get('/:userId/stats', getUserStats);

module.exports = router;
