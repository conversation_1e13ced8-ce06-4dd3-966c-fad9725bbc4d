const express = require('express');
const {
  getMusicList,
  getMusicById,
  searchMusic,
  updateMusic,
  deleteMusic,
  reviewMusic,
  getMusicPlayUrl,
  getPopularMusic,
  getRecentMusic,
  getMusicStats,
  getMyMusic,
  getPendingMusic,
  batchReviewMusic,
  getReviewHistory,
  getReviewStats,
  advancedSearchMusic,
  getSearchSuggestions,
  getFilterOptions,
  getSimilarMusic,
  getRecommendedMusic,
  getGenreBasedRecommendations,
  getTrendingMusic,
  getDiscoverMusic,
  recordPlayBehavior,
  batchDeleteMusic,
  batchUpdateMusic,
  batchMoveMusic,
  batchExportMusic,
  getBatchOperationHistory,
  getDetailedMusicStats,
  getUserBehaviorAnalysis,
  getMusicTrendAnalysis,
  getGenreAnalysis,
  getArtistRanking,
  getSystemMetrics,
  generateStatisticsReport
} = require('../controllers/musicController');
const { authenticateToken, requireAdmin, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// 公开接口（不需要认证）
router.get('/popular', getPopularMusic);
router.get('/recent', getRecentMusic);
router.get('/trending', getTrendingMusic);
router.get('/search', searchMusic);
router.get('/search/advanced', advancedSearchMusic);
router.get('/search/suggestions', getSearchSuggestions);
router.get('/filters', getFilterOptions);
router.get('/recommendations/genre', optionalAuth, getGenreBasedRecommendations);
router.get('/', getMusicList);
// 注意：/:id 路由要放在最后，避免拦截其他路由

// 需要认证的接口
router.get('/:id/play', authenticateToken, getMusicPlayUrl);
router.get('/:id/similar', getSimilarMusic);
router.get('/my/uploads', authenticateToken, getMyMusic);
router.get('/recommendations', authenticateToken, getRecommendedMusic);
router.get('/discover', authenticateToken, getDiscoverMusic);

// 需要认证的修改接口
router.put('/:id', authenticateToken, updateMusic);
router.delete('/:id', authenticateToken, deleteMusic);
router.post('/:id/play-behavior', authenticateToken, recordPlayBehavior);

// 管理员接口 - 音乐管理
router.get('/admin/stats', requireAdmin, getMusicStats);
router.get('/admin/pending', requireAdmin, getPendingMusic);

// 管理员接口 - 审核功能
router.post('/:id/review', requireAdmin, reviewMusic);
router.post('/admin/batch-review', requireAdmin, batchReviewMusic);
router.get('/admin/review-history', requireAdmin, getReviewHistory);
router.get('/admin/review-stats', requireAdmin, getReviewStats);

// 管理员接口 - 批量操作
router.post('/admin/batch-delete', requireAdmin, batchDeleteMusic);
router.post('/admin/batch-update', requireAdmin, batchUpdateMusic);
router.post('/admin/batch-move', requireAdmin, batchMoveMusic);
router.post('/admin/batch-export', requireAdmin, batchExportMusic);
router.get('/admin/batch-operations', requireAdmin, getBatchOperationHistory);

// 管理员接口 - 统计分析
router.get('/admin/stats/detailed', requireAdmin, getDetailedMusicStats);
router.get('/admin/stats/user-behavior', requireAdmin, getUserBehaviorAnalysis);
router.get('/admin/stats/trends', requireAdmin, getMusicTrendAnalysis);
router.get('/admin/stats/genres', requireAdmin, getGenreAnalysis);
router.get('/admin/stats/artists', requireAdmin, getArtistRanking);
router.get('/admin/stats/system', requireAdmin, getSystemMetrics);
router.post('/admin/reports/generate', requireAdmin, generateStatisticsReport);

// /:id 路由放在最后，避免拦截其他路由
router.get('/:id', getMusicById);

module.exports = router;
