const express = require('express');
const {
  startPlay,
  pausePlay,
  resumePlay,
  stopPlay,
  skipSong,
  previousSong,
  nextSong,
  setPlayMode,
  setVolume,
  getPlayStatus,
  updateProgress,
  seekTo,
  setPlayQuality
} = require('../controllers/playController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 所有播放相关路由都需要认证
router.use(authenticateToken);

// 播放控制路由
router.post('/start', startPlay);           // 开始播放
router.post('/pause', pausePlay);           // 暂停播放
router.post('/resume', resumePlay);         // 恢复播放
router.post('/stop', stopPlay);             // 停止播放
router.post('/skip', skipSong);             // 跳过当前歌曲
router.post('/previous', previousSong);     // 播放上一首
router.post('/next', nextSong);             // 播放下一首

// 播放设置路由
router.post('/mode', setPlayMode);          // 设置播放模式
router.post('/volume', setVolume);          // 设置音量
router.post('/quality', setPlayQuality);    // 设置播放质量

// 播放状态路由
router.get('/status', getPlayStatus);       // 获取播放状态
router.post('/progress', updateProgress);   // 更新播放进度（心跳）
router.post('/seek', seekTo);               // 跳转到指定位置

module.exports = router;
