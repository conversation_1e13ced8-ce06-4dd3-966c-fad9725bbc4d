const Follow = require('../models/Follow');
const User = require('../models/User');
const mongoose = require('mongoose');

/**
 * 关注用户
 * POST /api/v1/follows/:userId
 */
const followUser = async (req, res) => {
  try {
    const followerId = req.user._id;
    const followingId = req.params.userId;
    const { source = 'manual' } = req.body;

    // 验证用户ID
    if (!mongoose.Types.ObjectId.isValid(followingId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    // 检查被关注用户是否存在
    const userToFollow = await User.findById(followingId);
    if (!userToFollow) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // 检查是否已经关注
    const existingFollow = await Follow.findOne({
      follower: followerId,
      following: followingId
    });

    if (existingFollow) {
      if (existingFollow.status === 'active') {
        return res.status(400).json({
          success: false,
          message: 'Already following this user'
        });
      } else {
        // 重新激活关注
        existingFollow.status = 'active';
        existingFollow.source = source;
        existingFollow.createdAt = new Date();
        await existingFollow.save();
        await existingFollow.checkMutualFollow();

        return res.status(200).json({
          success: true,
          message: 'Successfully followed user',
          data: {
            follow: existingFollow,
            isMutual: existingFollow.isMutual
          }
        });
      }
    }

    // 创建新的关注关系
    const newFollow = new Follow({
      follower: followerId,
      following: followingId,
      source,
      status: 'active'
    });

    await newFollow.save();
    await newFollow.checkMutualFollow();

    // 填充用户信息
    await newFollow.populate('following', 'username avatar profile.displayName profile.bio');

    res.status(201).json({
      success: true,
      message: 'Successfully followed user',
      data: {
        follow: newFollow,
        isMutual: newFollow.isMutual
      }
    });

  } catch (error) {
    console.error('Follow user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to follow user',
      error: error.message
    });
  }
};

/**
 * 取消关注用户
 * DELETE /api/v1/follows/:userId
 */
const unfollowUser = async (req, res) => {
  try {
    const followerId = req.user._id;
    const followingId = req.params.userId;

    // 验证用户ID
    if (!mongoose.Types.ObjectId.isValid(followingId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    // 查找并删除关注关系
    const follow = await Follow.findOneAndDelete({
      follower: followerId,
      following: followingId
    });

    if (!follow) {
      return res.status(404).json({
        success: false,
        message: 'Follow relationship not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Successfully unfollowed user',
      data: {
        unfollowedUser: followingId,
        wasMutual: follow.isMutual
      }
    });

  } catch (error) {
    console.error('Unfollow user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unfollow user',
      error: error.message
    });
  }
};

/**
 * 获取用户的关注列表
 * GET /api/v1/follows/:userId/following
 */
const getFollowing = async (req, res) => {
  try {
    const userId = req.params.userId;
    const {
      page = 1,
      limit = 20,
      sort = 'recent', // recent, oldest, mutual
      status = 'active'
    } = req.query;

    // 验证用户ID
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    const skip = (page - 1) * limit;
    let sortOption = { createdAt: -1 };

    switch (sort) {
      case 'oldest':
        sortOption = { createdAt: 1 };
        break;
      case 'mutual':
        sortOption = { isMutual: -1, createdAt: -1 };
        break;
      default:
        sortOption = { createdAt: -1 };
    }

    const following = await Follow.getFollowing(userId, {
      status,
      limit: parseInt(limit),
      skip,
      sort: sortOption,
      populate: true
    });

    const totalCount = await Follow.countDocuments({
      follower: userId,
      status
    });

    res.status(200).json({
      success: true,
      data: {
        following,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNext: skip + following.length < totalCount,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get following error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get following list',
      error: error.message
    });
  }
};

/**
 * 获取用户的粉丝列表
 * GET /api/v1/follows/:userId/followers
 */
const getFollowers = async (req, res) => {
  try {
    const userId = req.params.userId;
    const {
      page = 1,
      limit = 20,
      sort = 'recent', // recent, oldest, mutual
      status = 'active'
    } = req.query;

    // 验证用户ID
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    const skip = (page - 1) * limit;
    let sortOption = { createdAt: -1 };

    switch (sort) {
      case 'oldest':
        sortOption = { createdAt: 1 };
        break;
      case 'mutual':
        sortOption = { isMutual: -1, createdAt: -1 };
        break;
      default:
        sortOption = { createdAt: -1 };
    }

    const followers = await Follow.getFollowers(userId, {
      status,
      limit: parseInt(limit),
      skip,
      sort: sortOption,
      populate: true
    });

    const totalCount = await Follow.countDocuments({
      following: userId,
      status
    });

    res.status(200).json({
      success: true,
      data: {
        followers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNext: skip + followers.length < totalCount,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get followers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get followers list',
      error: error.message
    });
  }
};

/**
 * 获取相互关注列表
 * GET /api/v1/follows/mutual
 */
const getMutualFollows = async (req, res) => {
  try {
    const userId = req.user._id;
    const {
      page = 1,
      limit = 20,
      sort = 'recent'
    } = req.query;

    const skip = (page - 1) * limit;
    let sortOption = { createdAt: -1 };

    if (sort === 'oldest') {
      sortOption = { createdAt: 1 };
    }

    const mutualFollows = await Follow.getMutualFollows(userId, {
      limit: parseInt(limit),
      skip,
      sort: sortOption
    });

    const totalCount = await Follow.countDocuments({
      follower: userId,
      status: 'active',
      isMutual: true
    });

    res.status(200).json({
      success: true,
      data: {
        mutualFollows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNext: skip + mutualFollows.length < totalCount,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get mutual follows error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get mutual follows',
      error: error.message
    });
  }
};

/**
 * 检查关注状态
 * GET /api/v1/follows/:userId/status
 */
const checkFollowStatus = async (req, res) => {
  try {
    const followerId = req.user._id;
    const followingId = req.params.userId;

    // 验证用户ID
    if (!mongoose.Types.ObjectId.isValid(followingId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    const followStatus = await Follow.checkFollowStatus(followerId, followingId);
    const reverseFollowStatus = await Follow.checkFollowStatus(followingId, followerId);

    res.status(200).json({
      success: true,
      data: {
        isFollowing: !!followStatus && followStatus.status === 'active',
        isFollowedBy: !!reverseFollowStatus && reverseFollowStatus.status === 'active',
        isMutual: !!followStatus && !!reverseFollowStatus &&
                  followStatus.status === 'active' && reverseFollowStatus.status === 'active',
        followedAt: followStatus ? followStatus.createdAt : null,
        followedByAt: reverseFollowStatus ? reverseFollowStatus.createdAt : null
      }
    });

  } catch (error) {
    console.error('Check follow status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check follow status',
      error: error.message
    });
  }
};

/**
 * 获取用户统计信息
 * GET /api/v1/follows/:userId/stats
 */
const getUserStats = async (req, res) => {
  try {
    const userId = req.params.userId;

    // 验证用户ID
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID'
      });
    }

    const stats = await Follow.getUserStats(userId);

    res.status(200).json({
      success: true,
      data: {
        stats,
        userId
      }
    });

  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user stats',
      error: error.message
    });
  }
};

/**
 * 获取推荐关注用户
 * GET /api/v1/follows/recommendations
 */
const getRecommendedUsers = async (req, res) => {
  try {
    const userId = req.user._id;
    const { limit = 10 } = req.query;

    const recommendations = await Follow.getRecommendedUsers(userId, parseInt(limit));

    res.status(200).json({
      success: true,
      data: {
        recommendations,
        count: recommendations.length
      }
    });

  } catch (error) {
    console.error('Get recommended users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommended users',
      error: error.message
    });
  }
};

/**
 * 批量关注用户
 * POST /api/v1/follows/batch
 */
const batchFollowUsers = async (req, res) => {
  try {
    const followerId = req.user._id;
    const { userIds, source = 'manual' } = req.body;

    console.log('Batch follow request:', { followerId, userIds, source });

    if (!Array.isArray(userIds) || userIds.length === 0) {
      console.log('Invalid userIds array:', userIds);
      return res.status(400).json({
        success: false,
        message: 'User IDs array is required'
      });
    }

    if (userIds.length > 50) {
      return res.status(400).json({
        success: false,
        message: 'Cannot follow more than 50 users at once'
      });
    }

    const results = {
      successful: [],
      failed: [],
      alreadyFollowing: []
    };

    for (const userId of userIds) {
      try {
        // 验证用户ID
        if (!mongoose.Types.ObjectId.isValid(userId)) {
          results.failed.push({ userId, reason: 'Invalid user ID' });
          continue;
        }

        // 检查用户是否存在
        const userExists = await User.findById(userId);
        if (!userExists) {
          results.failed.push({ userId, reason: 'User not found' });
          continue;
        }

        // 检查是否已经关注
        const existingFollow = await Follow.findOne({
          follower: followerId,
          following: userId
        });

        if (existingFollow && existingFollow.status === 'active') {
          results.alreadyFollowing.push({ userId, username: userExists.username });
          continue;
        }

        // 创建或更新关注关系
        let follow;
        if (existingFollow) {
          existingFollow.status = 'active';
          existingFollow.source = source;
          existingFollow.createdAt = new Date();
          follow = await existingFollow.save();
        } else {
          follow = new Follow({
            follower: followerId,
            following: userId,
            source,
            status: 'active'
          });
          await follow.save();
        }

        await follow.checkMutualFollow();
        results.successful.push({
          userId,
          username: userExists.username,
          isMutual: follow.isMutual
        });

      } catch (error) {
        results.failed.push({ userId, reason: error.message });
      }
    }

    res.status(200).json({
      success: true,
      message: 'Batch follow operation completed',
      data: {
        results,
        summary: {
          total: userIds.length,
          successful: results.successful.length,
          failed: results.failed.length,
          alreadyFollowing: results.alreadyFollowing.length
        }
      }
    });

  } catch (error) {
    console.error('Batch follow users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch follow users',
      error: error.message
    });
  }
};

module.exports = {
  followUser,
  unfollowUser,
  getFollowing,
  getFollowers,
  getMutualFollows,
  checkFollowStatus,
  getUserStats,
  getRecommendedUsers,
  batchFollowUsers
};
