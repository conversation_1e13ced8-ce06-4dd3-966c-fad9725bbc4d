const MusicService = require('../services/musicService');

/**
 * 获取音乐列表
 */
const getMusicList = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      quality,
      genre,
      artist,
      sortBy = 'newest'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      quality,
      genre,
      artist,
      sortBy
    };

    const result = await MusicService.getMusicList(options);

    res.status(200).json({
      success: true,
      message: 'Music list retrieved successfully',
      data: result
    });
  } catch (error) {
    console.error('Get music list error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get music list',
      error: error.message
    });
  }
};

/**
 * 获取音乐详情
 */
const getMusicById = async (req, res) => {
  try {
    const { id } = req.params;
    const includePrivate = req.user && (req.user.userGroup === 'admin' || req.user._id.toString() === req.query.uploadedBy);

    const music = await MusicService.getMusicById(id, includePrivate);

    res.status(200).json({
      success: true,
      message: 'Music details retrieved successfully',
      data: music
    });
  } catch (error) {
    console.error('Get music by ID error:', error);
    const statusCode = error.message === 'Music not found' ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 搜索音乐
 */
const searchMusic = async (req, res) => {
  try {
    const {
      q: query,
      page = 1,
      limit = 20,
      sortBy = 'relevance',
      quality,
      genre,
      artist
    } = req.query;

    if (!query || query.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      quality,
      genre,
      artist
    };

    const result = await MusicService.searchMusic(query.trim(), options);

    res.status(200).json({
      success: true,
      message: 'Music search completed successfully',
      data: result
    });
  } catch (error) {
    console.error('Search music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search music',
      error: error.message
    });
  }
};

/**
 * 更新音乐信息
 */
const updateMusic = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user._id;

    // 过滤不允许更新的字段
    const allowedFields = [
      'title', 'artist', 'album', 'genre', 'year',
      'tags', 'lyrics', 'hasLyrics'
    ];
    
    const filteredData = {};
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields to update'
      });
    }

    const music = await MusicService.updateMusic(id, filteredData, userId);

    res.status(200).json({
      success: true,
      message: 'Music updated successfully',
      data: music
    });
  } catch (error) {
    console.error('Update music error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 
                      error.message.includes('Permission denied') ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 删除音乐
 */
const deleteMusic = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    await MusicService.deleteMusic(id, userId);

    res.status(200).json({
      success: true,
      message: 'Music deleted successfully'
    });
  } catch (error) {
    console.error('Delete music error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 
                      error.message.includes('Permission denied') ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 审核音乐（管理员功能）
 */
const reviewMusic = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reviewNote } = req.body;
    const reviewerId = req.user._id;

    // 验证状态
    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be "approved" or "rejected"'
      });
    }

    const music = await MusicService.reviewMusic(id, status, reviewerId, reviewNote);

    res.status(200).json({
      success: true,
      message: `Music ${status} successfully`,
      data: music
    });
  } catch (error) {
    console.error('Review music error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 获取音乐播放URL
 */
const getMusicPlayUrl = async (req, res) => {
  try {
    const { id } = req.params;
    const { expiry = 24 * 60 * 60 } = req.query; // 默认24小时

    const result = await MusicService.getMusicPlayUrl(id, parseInt(expiry));

    res.status(200).json({
      success: true,
      message: 'Play URL generated successfully',
      data: result
    });
  } catch (error) {
    console.error('Get play URL error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 获取热门音乐
 */
const getPopularMusic = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const music = await MusicService.getPopularMusic(parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Popular music retrieved successfully',
      data: music
    });
  } catch (error) {
    console.error('Get popular music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular music',
      error: error.message
    });
  }
};

/**
 * 获取最新音乐
 */
const getRecentMusic = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const music = await MusicService.getRecentMusic(parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Recent music retrieved successfully',
      data: music
    });
  } catch (error) {
    console.error('Get recent music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recent music',
      error: error.message
    });
  }
};

/**
 * 获取音乐统计信息（管理员功能）
 */
const getMusicStats = async (req, res) => {
  try {
    const stats = await MusicService.getMusicStats();

    res.status(200).json({
      success: true,
      message: 'Music statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    console.error('Get music stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get music statistics',
      error: error.message
    });
  }
};

/**
 * 获取我上传的音乐
 */
const getMyMusic = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'newest'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      uploadedBy: req.user._id,
      status,
      sortBy
    };

    const result = await MusicService.getMusicList(options);

    res.status(200).json({
      success: true,
      message: 'My music list retrieved successfully',
      data: result
    });
  } catch (error) {
    console.error('Get my music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get my music list',
      error: error.message
    });
  }
};

/**
 * 获取待审核音乐列表（管理员功能）
 */
const getPendingMusic = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sortBy = 'oldest'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      status: 'pending',
      sortBy
    };

    const result = await MusicService.getMusicList(options);

    res.status(200).json({
      success: true,
      message: 'Pending music list retrieved successfully',
      data: result
    });
  } catch (error) {
    console.error('Get pending music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get pending music list',
      error: error.message
    });
  }
};

/**
 * 批量审核音乐（管理员功能）
 */
const batchReviewMusic = async (req, res) => {
  try {
    const { musicIds, status, reviewNote } = req.body;
    const reviewerId = req.user._id;

    // 验证输入
    if (!Array.isArray(musicIds) || musicIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Music IDs array is required and cannot be empty'
      });
    }

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be "approved" or "rejected"'
      });
    }

    const result = await MusicService.batchReviewMusic(musicIds, status, reviewerId, reviewNote);

    res.status(200).json({
      success: true,
      message: `Batch review completed. ${result.successCount} music ${status}, ${result.failedCount} failed`,
      data: result
    });
  } catch (error) {
    console.error('Batch review music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch review music',
      error: error.message
    });
  }
};

/**
 * 获取审核历史（管理员功能）
 */
const getReviewHistory = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      reviewerId,
      status,
      startDate,
      endDate
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      reviewerId,
      status,
      startDate,
      endDate
    };

    const result = await MusicService.getReviewHistory(options);

    res.status(200).json({
      success: true,
      message: 'Review history retrieved successfully',
      data: result
    });
  } catch (error) {
    console.error('Get review history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get review history',
      error: error.message
    });
  }
};

/**
 * 获取审核统计信息（管理员功能）
 */
const getReviewStats = async (req, res) => {
  try {
    const { period = '7d' } = req.query; // 7d, 30d, 90d, all
    const stats = await MusicService.getReviewStats(period);

    res.status(200).json({
      success: true,
      message: 'Review statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    console.error('Get review stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get review statistics',
      error: error.message
    });
  }
};

/**
 * 高级搜索音乐
 */
const advancedSearchMusic = async (req, res) => {
  try {
    const {
      q: query,
      page = 1,
      limit = 20,
      sortBy = 'relevance',
      // 基本过滤器
      quality,
      genre,
      artist,
      album,
      year,
      // 高级过滤器
      minDuration,
      maxDuration,
      minBitrate,
      maxBitrate,
      uploadedBy,
      dateRange,
      tags,
      hasLyrics,
      // 搜索选项
      exactMatch = false,
      searchFields = ['title', 'artist', 'album']
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      quality,
      genre,
      artist,
      album,
      year: year ? parseInt(year) : undefined,
      minDuration: minDuration ? parseInt(minDuration) : undefined,
      maxDuration: maxDuration ? parseInt(maxDuration) : undefined,
      minBitrate: minBitrate ? parseInt(minBitrate) : undefined,
      maxBitrate: maxBitrate ? parseInt(maxBitrate) : undefined,
      uploadedBy,
      dateRange,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : undefined,
      hasLyrics: hasLyrics === 'true',
      exactMatch: exactMatch === 'true',
      searchFields: Array.isArray(searchFields) ? searchFields : searchFields.split(',')
    };

    const result = await MusicService.advancedSearchMusic(query, options);

    res.status(200).json({
      success: true,
      message: 'Advanced search completed successfully',
      data: result
    });
  } catch (error) {
    console.error('Advanced search error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform advanced search',
      error: error.message
    });
  }
};

/**
 * 获取搜索建议
 */
const getSearchSuggestions = async (req, res) => {
  try {
    const { q: query, type = 'all', limit = 10 } = req.query;

    if (!query || query.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    const suggestions = await MusicService.getSearchSuggestions(query.trim(), type, parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Search suggestions retrieved successfully',
      data: suggestions
    });
  } catch (error) {
    console.error('Get search suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get search suggestions',
      error: error.message
    });
  }
};

/**
 * 获取过滤器选项
 */
const getFilterOptions = async (req, res) => {
  try {
    const options = await MusicService.getFilterOptions();

    res.status(200).json({
      success: true,
      message: 'Filter options retrieved successfully',
      data: options
    });
  } catch (error) {
    console.error('Get filter options error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get filter options',
      error: error.message
    });
  }
};

/**
 * 获取相似音乐
 */
const getSimilarMusic = async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 10 } = req.query;

    const similarMusic = await MusicService.getSimilarMusic(id, parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Similar music retrieved successfully',
      data: similarMusic
    });
  } catch (error) {
    console.error('Get similar music error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 获取个性化推荐音乐
 */
const getRecommendedMusic = async (req, res) => {
  try {
    const { limit = 20, algorithm = 'hybrid' } = req.query;
    const userId = req.user._id;

    const recommendations = await MusicService.getRecommendedMusic(userId, {
      limit: parseInt(limit),
      algorithm
    });

    res.status(200).json({
      success: true,
      message: 'Recommended music retrieved successfully',
      data: recommendations
    });
  } catch (error) {
    console.error('Get recommended music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommended music',
      error: error.message
    });
  }
};

/**
 * 获取基于流派的推荐
 */
const getGenreBasedRecommendations = async (req, res) => {
  try {
    const { genre, limit = 10 } = req.query;
    const userId = req.user?._id;

    if (!genre) {
      return res.status(400).json({
        success: false,
        message: 'Genre parameter is required'
      });
    }

    const recommendations = await MusicService.getGenreBasedRecommendations(genre, userId, parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Genre-based recommendations retrieved successfully',
      data: recommendations
    });
  } catch (error) {
    console.error('Get genre-based recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get genre-based recommendations',
      error: error.message
    });
  }
};

/**
 * 获取热门趋势音乐
 */
const getTrendingMusic = async (req, res) => {
  try {
    const { period = '7d', limit = 20 } = req.query;

    const trendingMusic = await MusicService.getTrendingMusic(period, parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Trending music retrieved successfully',
      data: trendingMusic
    });
  } catch (error) {
    console.error('Get trending music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trending music',
      error: error.message
    });
  }
};

/**
 * 获取新发现音乐（为用户推荐未听过的音乐）
 */
const getDiscoverMusic = async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    const userId = req.user._id;

    const discoverMusic = await MusicService.getDiscoverMusic(userId, parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Discover music retrieved successfully',
      data: discoverMusic
    });
  } catch (error) {
    console.error('Get discover music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get discover music',
      error: error.message
    });
  }
};

/**
 * 记录用户播放行为（用于推荐算法）
 */
const recordPlayBehavior = async (req, res) => {
  try {
    const { musicId } = req.params;
    const { duration, completed = false } = req.body;
    const userId = req.user._id;

    await MusicService.recordPlayBehavior(userId, musicId, {
      duration: parseInt(duration),
      completed
    });

    res.status(200).json({
      success: true,
      message: 'Play behavior recorded successfully'
    });
  } catch (error) {
    console.error('Record play behavior error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record play behavior',
      error: error.message
    });
  }
};

/**
 * 批量删除音乐（管理员功能）
 */
const batchDeleteMusic = async (req, res) => {
  try {
    const { musicIds, force = false } = req.body;
    const userId = req.user._id;

    // 验证输入
    if (!Array.isArray(musicIds) || musicIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Music IDs array is required and cannot be empty'
      });
    }

    const result = await MusicService.batchDeleteMusic(musicIds, userId, force);

    res.status(200).json({
      success: true,
      message: `Batch delete completed. ${result.successCount} music deleted, ${result.failedCount} failed`,
      data: result
    });
  } catch (error) {
    console.error('Batch delete music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch delete music',
      error: error.message
    });
  }
};

/**
 * 批量更新音乐信息（管理员功能）
 */
const batchUpdateMusic = async (req, res) => {
  try {
    const { musicIds, updateData } = req.body;
    const userId = req.user._id;

    // 验证输入
    if (!Array.isArray(musicIds) || musicIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Music IDs array is required and cannot be empty'
      });
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Update data is required'
      });
    }

    const result = await MusicService.batchUpdateMusic(musicIds, updateData, userId);

    res.status(200).json({
      success: true,
      message: `Batch update completed. ${result.successCount} music updated, ${result.failedCount} failed`,
      data: result
    });
  } catch (error) {
    console.error('Batch update music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch update music',
      error: error.message
    });
  }
};

/**
 * 批量移动音乐到指定状态（管理员功能）
 */
const batchMoveMusic = async (req, res) => {
  try {
    const { musicIds, targetStatus, reason } = req.body;
    const userId = req.user._id;

    // 验证输入
    if (!Array.isArray(musicIds) || musicIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Music IDs array is required and cannot be empty'
      });
    }

    if (!['pending', 'approved', 'rejected'].includes(targetStatus)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid target status. Must be "pending", "approved", or "rejected"'
      });
    }

    const result = await MusicService.batchMoveMusic(musicIds, targetStatus, userId, reason);

    res.status(200).json({
      success: true,
      message: `Batch move completed. ${result.successCount} music moved to ${targetStatus}, ${result.failedCount} failed`,
      data: result
    });
  } catch (error) {
    console.error('Batch move music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch move music',
      error: error.message
    });
  }
};

/**
 * 批量导出音乐信息（管理员功能）
 */
const batchExportMusic = async (req, res) => {
  try {
    const { musicIds, format = 'json', fields } = req.body;

    // 验证输入
    if (!Array.isArray(musicIds) || musicIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Music IDs array is required and cannot be empty'
      });
    }

    if (!['json', 'csv'].includes(format)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid format. Must be "json" or "csv"'
      });
    }

    const result = await MusicService.batchExportMusic(musicIds, format, fields);

    // 设置响应头
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `music_export_${timestamp}.${format}`;

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', format === 'json' ? 'application/json' : 'text/csv');

    res.status(200).send(result);
  } catch (error) {
    console.error('Batch export music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch export music',
      error: error.message
    });
  }
};

/**
 * 获取批量操作历史（管理员功能）
 */
const getBatchOperationHistory = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      operationType,
      operatorId,
      startDate,
      endDate
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      operationType,
      operatorId,
      startDate,
      endDate
    };

    const result = await MusicService.getBatchOperationHistory(options);

    res.status(200).json({
      success: true,
      message: 'Batch operation history retrieved successfully',
      data: result
    });
  } catch (error) {
    console.error('Get batch operation history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get batch operation history',
      error: error.message
    });
  }
};

/**
 * 获取详细音乐统计报表（管理员功能）
 */
const getDetailedMusicStats = async (req, res) => {
  try {
    const { period = '30d', groupBy = 'day' } = req.query;

    const stats = await MusicService.getDetailedMusicStats(period, groupBy);

    res.status(200).json({
      success: true,
      message: 'Detailed music statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    console.error('Get detailed music stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get detailed music statistics',
      error: error.message
    });
  }
};

/**
 * 获取用户行为分析（管理员功能）
 */
const getUserBehaviorAnalysis = async (req, res) => {
  try {
    const { period = '30d', analysisType = 'overview' } = req.query;

    const analysis = await MusicService.getUserBehaviorAnalysis(period, analysisType);

    res.status(200).json({
      success: true,
      message: 'User behavior analysis retrieved successfully',
      data: analysis
    });
  } catch (error) {
    console.error('Get user behavior analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user behavior analysis',
      error: error.message
    });
  }
};

/**
 * 获取音乐趋势分析（管理员功能）
 */
const getMusicTrendAnalysis = async (req, res) => {
  try {
    const { period = '90d', trendType = 'popularity' } = req.query;

    const trends = await MusicService.getMusicTrendAnalysis(period, trendType);

    res.status(200).json({
      success: true,
      message: 'Music trend analysis retrieved successfully',
      data: trends
    });
  } catch (error) {
    console.error('Get music trend analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get music trend analysis',
      error: error.message
    });
  }
};

/**
 * 获取流派分析报告（管理员功能）
 */
const getGenreAnalysis = async (req, res) => {
  try {
    const { period = '30d', limit = 20 } = req.query;

    const analysis = await MusicService.getGenreAnalysis(period, parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Genre analysis retrieved successfully',
      data: analysis
    });
  } catch (error) {
    console.error('Get genre analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get genre analysis',
      error: error.message
    });
  }
};

/**
 * 获取艺术家排行榜（管理员功能）
 */
const getArtistRanking = async (req, res) => {
  try {
    const { period = '30d', rankBy = 'playCount', limit = 50 } = req.query;

    const ranking = await MusicService.getArtistRanking(period, rankBy, parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Artist ranking retrieved successfully',
      data: ranking
    });
  } catch (error) {
    console.error('Get artist ranking error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get artist ranking',
      error: error.message
    });
  }
};

/**
 * 获取系统性能指标（管理员功能）
 */
const getSystemMetrics = async (req, res) => {
  try {
    const { period = '24h' } = req.query;

    const metrics = await MusicService.getSystemMetrics(period);

    res.status(200).json({
      success: true,
      message: 'System metrics retrieved successfully',
      data: metrics
    });
  } catch (error) {
    console.error('Get system metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system metrics',
      error: error.message
    });
  }
};

/**
 * 生成统计报告（管理员功能）
 */
const generateStatisticsReport = async (req, res) => {
  try {
    const {
      reportType = 'comprehensive',
      period = '30d',
      format = 'json',
      includeCharts = false
    } = req.body;

    const report = await MusicService.generateStatisticsReport({
      reportType,
      period,
      format,
      includeCharts
    });

    // 设置响应头
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `music_stats_report_${timestamp}.${format}`;

    if (format !== 'json') {
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : 'text/html');
    }

    res.status(200).json({
      success: true,
      message: 'Statistics report generated successfully',
      data: report
    });
  } catch (error) {
    console.error('Generate statistics report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate statistics report',
      error: error.message
    });
  }
};

module.exports = {
  getMusicList,
  getMusicById,
  searchMusic,
  updateMusic,
  deleteMusic,
  reviewMusic,
  getMusicPlayUrl,
  getPopularMusic,
  getRecentMusic,
  getMusicStats,
  getMyMusic,
  getPendingMusic,
  batchReviewMusic,
  getReviewHistory,
  getReviewStats,
  advancedSearchMusic,
  getSearchSuggestions,
  getFilterOptions,
  getSimilarMusic,
  getRecommendedMusic,
  getGenreBasedRecommendations,
  getTrendingMusic,
  getDiscoverMusic,
  recordPlayBehavior,
  batchDeleteMusic,
  batchUpdateMusic,
  batchMoveMusic,
  batchExportMusic,
  getBatchOperationHistory,
  getDetailedMusicStats,
  getUserBehaviorAnalysis,
  getMusicTrendAnalysis,
  getGenreAnalysis,
  getArtistRanking,
  getSystemMetrics,
  generateStatisticsReport
};
