const AudioQualityService = require('../services/audioQualityService');
const Music = require('../models/Music');
const multer = require('multer');
const path = require('path');

// 配置multer用于临时文件上传
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB限制
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedMimes = [
      'audio/mpeg',
      'audio/mp3',
      'audio/wav',
      'audio/flac',
      'audio/aac',
      'audio/m4a',
      'audio/ogg',
      'audio/x-wav',
      'audio/x-flac'
    ];
    
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only audio files are allowed.'), false);
    }
  }
});

class AudioQualityController {
  /**
   * 分析单个音频文件质量
   * POST /api/v1/audio-quality/analyze
   */
  static async analyzeAudioFile(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No audio file provided'
        });
      }

      const { buffer, originalname } = req.file;
      
      console.log(`Starting quality analysis for: ${originalname}`);
      
      // 使用AudioQualityService分析音频质量
      const qualityAnalysis = await AudioQualityService.analyzeAudioQuality(buffer, originalname);
      
      // 获取格式建议
      const recommendations = AudioQualityService.getFormatRecommendations(qualityAnalysis);
      
      res.json({
        success: true,
        message: 'Audio quality analysis completed',
        data: {
          fileName: originalname,
          fileSize: buffer.length,
          analysis: qualityAnalysis,
          recommendations: recommendations,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Audio quality analysis error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to analyze audio quality',
        error: error.message
      });
    }
  }

  /**
   * 批量分析音频文件质量
   * POST /api/v1/audio-quality/batch-analyze
   */
  static async batchAnalyzeAudioFiles(req, res) {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No audio files provided'
        });
      }

      console.log(`Starting batch analysis for ${req.files.length} files`);
      
      // 准备文件数据
      const audioFiles = req.files.map(file => ({
        buffer: file.buffer,
        name: file.originalname
      }));
      
      // 批量分析
      const results = await AudioQualityService.batchAnalyzeQuality(audioFiles);
      
      // 统计结果
      const stats = {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        qualityDistribution: {}
      };
      
      // 统计质量分布
      results.forEach(result => {
        if (result.success) {
          const level = result.analysis.qualityLevel;
          stats.qualityDistribution[level] = (stats.qualityDistribution[level] || 0) + 1;
        }
      });
      
      res.json({
        success: true,
        message: 'Batch audio quality analysis completed',
        data: {
          results: results,
          statistics: stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Batch audio quality analysis error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to perform batch audio quality analysis',
        error: error.message
      });
    }
  }

  /**
   * 获取已上传音乐的质量报告
   * GET /api/v1/audio-quality/report/:musicId
   */
  static async getMusicQualityReport(req, res) {
    try {
      const { musicId } = req.params;
      
      // 查找音乐记录
      const music = await Music.findById(musicId);
      if (!music) {
        return res.status(404).json({
          success: false,
          message: 'Music not found'
        });
      }
      
      // 检查是否有质量分析数据
      if (!music.qualityAnalysis) {
        return res.status(404).json({
          success: false,
          message: 'Quality analysis data not available for this music'
        });
      }
      
      // 生成详细报告
      const report = {
        musicInfo: {
          id: music._id,
          title: music.title,
          artist: music.artist,
          album: music.album,
          uploadDate: music.createdAt
        },
        qualityAnalysis: music.qualityAnalysis,
        recommendations: AudioQualityService.getFormatRecommendations(music.qualityAnalysis),
        technicalDetails: {
          duration: music.duration,
          bitrate: music.bitrate,
          sampleRate: music.sampleRate,
          fileSize: music.fileSize,
          format: music.format,
          quality: music.quality
        }
      };
      
      res.json({
        success: true,
        message: 'Quality report generated successfully',
        data: report
      });

    } catch (error) {
      console.error('Quality report generation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate quality report',
        error: error.message
      });
    }
  }

  /**
   * 获取质量统计信息
   * GET /api/v1/audio-quality/statistics
   */
  static async getQualityStatistics(req, res) {
    try {
      const { userId } = req.query;
      
      // 构建查询条件
      const query = {};
      if (userId) {
        query.uploadedBy = userId;
      }
      
      // 聚合查询获取统计信息
      const stats = await Music.aggregate([
        { $match: query },
        {
          $group: {
            _id: '$quality',
            count: { $sum: 1 },
            avgBitrate: { $avg: '$bitrate' },
            avgDuration: { $avg: '$duration' },
            totalSize: { $sum: '$fileSize' }
          }
        },
        { $sort: { count: -1 } }
      ]);
      
      // 计算总体统计
      const totalMusic = await Music.countDocuments(query);
      const avgQualityScore = await Music.aggregate([
        { $match: { ...query, 'qualityAnalysis.qualityScore': { $exists: true } } },
        { $group: { _id: null, avgScore: { $avg: '$qualityAnalysis.qualityScore' } } }
      ]);
      
      res.json({
        success: true,
        message: 'Quality statistics retrieved successfully',
        data: {
          totalMusic: totalMusic,
          averageQualityScore: avgQualityScore[0]?.avgScore || 0,
          qualityDistribution: stats,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('Quality statistics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve quality statistics',
        error: error.message
      });
    }
  }

  /**
   * 重新分析已上传音乐的质量
   * POST /api/v1/audio-quality/reanalyze/:musicId
   */
  static async reanalyzeMusicQuality(req, res) {
    try {
      const { musicId } = req.params;
      
      // 查找音乐记录
      const music = await Music.findById(musicId);
      if (!music) {
        return res.status(404).json({
          success: false,
          message: 'Music not found'
        });
      }
      
      // 检查权限（只有上传者或管理员可以重新分析）
      if (music.uploadedBy.toString() !== req.user.id && req.user.userGroup !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Permission denied'
        });
      }
      
      // TODO: 从MinIO获取音频文件并重新分析
      // 这里需要实现从MinIO下载文件的逻辑
      
      res.json({
        success: false,
        message: 'Reanalysis feature not yet implemented',
        note: 'This feature requires MinIO file retrieval implementation'
      });

    } catch (error) {
      console.error('Music reanalysis error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reanalyze music quality',
        error: error.message
      });
    }
  }
}

// 导出控制器和multer配置
module.exports = {
  AudioQualityController,
  upload
};
