const axios = require('axios');

const API_BASE = 'http://localhost:3000/api/v1';

async function testPointsSystem() {
  try {
    console.log('🧪 Testing Points System...\n');
    
    // 1. Login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      identifier: 'testuser',
      password: 'password123'
    });
    
    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    
    console.log('✅ Login successful');
    console.log(`User: ${loginResponse.data.data.user.username}`);
    console.log(`Points: ${loginResponse.data.data.user.points}\n`);
    
    // 2. Get point records
    console.log('2. Getting point records...');
    const recordsResponse = await axios.get(`${API_BASE}/points/records`, { headers });
    
    console.log('✅ Point records retrieved');
    console.log(`Total records: ${recordsResponse.data.data.records.length}`);
    recordsResponse.data.data.records.forEach((record, index) => {
      console.log(`  ${index + 1}. ${record.type}: ${record.points > 0 ? '+' : ''}${record.points} points - ${record.description}`);
    });
    console.log('');
    
    // 3. Get point statistics
    console.log('3. Getting point statistics...');
    const statsResponse = await axios.get(`${API_BASE}/points/stats`, { headers });
    
    console.log('✅ Point statistics retrieved');
    console.log(`Total points: ${statsResponse.data.data.totalPoints}`);
    statsResponse.data.data.stats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.totalPoints} points (${stat.count} records)`);
    });
    console.log('');
    
    // 4. Test daily sign-in
    console.log('4. Testing daily sign-in...');
    const signinResponse = await axios.post(`${API_BASE}/auth/signin`, {}, { headers });
    
    console.log('✅ Daily sign-in result:');
    if (signinResponse.data.data.alreadySignedIn) {
      console.log('  Already signed in today');
    } else {
      console.log(`  Points earned: +${signinResponse.data.data.pointsEarned}`);
      console.log(`  Total points: ${signinResponse.data.data.totalPoints}`);
      console.log(`  Consecutive days: ${signinResponse.data.data.consecutiveDays}`);
    }
    console.log('');
    
    // 5. Get leaderboard
    console.log('5. Getting points leaderboard...');
    const leaderboardResponse = await axios.get(`${API_BASE}/points/leaderboard`);
    
    console.log('✅ Leaderboard retrieved');
    leaderboardResponse.data.data.leaderboard.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.username} (${user.displayName || 'No display name'}): ${user.totalPoints} points`);
    });
    console.log('');
    
    // 6. Get point types
    console.log('6. Getting available point types...');
    const typesResponse = await axios.get(`${API_BASE}/points/types`);
    
    console.log('✅ Point types retrieved');
    typesResponse.data.data.types.forEach(type => {
      console.log(`  ${type.value}: ${type.label} - ${type.description}`);
    });
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run tests
testPointsSystem();
